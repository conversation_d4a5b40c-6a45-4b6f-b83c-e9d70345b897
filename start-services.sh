#!/bin/bash

# BiteBase Intelligence - Start All Services
# This script starts both frontend and backend services

echo "🚀 Starting BiteBase Intelligence Services..."
echo "=============================================="

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  Port $port is already in use"
        return 1
    fi
    return 0
}

# Function to start backend
start_backend() {
    echo "📡 Starting Backend Service (Port 8002)..."
    cd backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        echo "❌ Virtual environment not found. Please run setup first."
        exit 1
    fi
    
    # Activate virtual environment and start backend
    source venv/bin/activate
    uvicorn app.main_simple:app --host 0.0.0.0 --port 8000 --reload &
    BACKEND_PID=$!
    echo "✅ Backend started with PID: $BACKEND_PID"
    cd ..
}

# Function to start frontend
start_frontend() {
    echo "🌐 Starting Frontend Service (Port 3000)..."
    cd frontend
    
    # Clean cache and start frontend
    rm -rf .next node_modules/.cache 2>/dev/null
    npm run dev -- --port 3000 &
    FRONTEND_PID=$!
    echo "✅ Frontend started with PID: $FRONTEND_PID"
    cd ..
}

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "✅ Backend stopped"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "✅ Frontend stopped"
    fi
    echo "👋 Goodbye!"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Check if ports are available
if ! check_port 3000; then
    echo "❌ Frontend port 3001 is busy. Please stop the existing service."
    exit 1
fi

if ! check_port 8000; then
    echo "❌ Backend port 8002 is busy. Please stop the existing service."
    exit 1
fi

# Start services
start_backend
sleep 3  # Give backend time to start

start_frontend
sleep 5  # Give frontend time to start

echo ""
echo "🎉 All services started successfully!"
echo "=============================================="
echo "🌐 Frontend: http://localhost:3000"
echo "📡 Backend:  http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"
echo "=============================================="

# Wait for services to run
wait
