# BiteBase CopilotKit Integration - Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered with the BiteBase CopilotKit Integration. Follow the troubleshooting steps in order for the best results.

## Quick Diagnostics

### System Health Check

1. **Check Service Status**
   ```bash
   # Check if services are running
   docker-compose ps
   
   # Check service logs
   docker-compose logs frontend
   docker-compose logs backend
   ```

2. **Verify API Connectivity**
   ```bash
   # Test backend health endpoint
   curl http://localhost:8000/api/v1/copilotkit/health
   
   # Test frontend
   curl http://localhost:3000/health
   ```

3. **Database Connection**
   ```bash
   # Test database connection
   psql -h localhost -U copilotkit_user -d bitebase_copilotkit -c "SELECT 1;"
   ```

## Common Issues and Solutions

### 1. AI Assistant Not Responding

#### Symptoms
- Chat interface loads but no responses
- "AI service unavailable" error messages
- Long response times or timeouts

#### Possible Causes
- API key issues
- Network connectivity problems
- Service overload
- Rate limiting

#### Solutions

**Check API Keys**
```bash
# Verify environment variables
echo $OPENAI_API_KEY
echo $ANTHROPIC_API_KEY

# Test API connectivity
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

**Verify Service Configuration**
```python
# Check backend configuration
from app.core.config import settings
print(f"OpenAI API Key configured: {bool(settings.OPENAI_API_KEY)}")
print(f"Anthropic API Key configured: {bool(settings.ANTHROPIC_API_KEY)}")
```

**Check Rate Limits**
```bash
# Check rate limit headers in logs
grep "rate.limit" /var/log/copilotkit/backend.log
```

**Restart Services**
```bash
# Restart backend service
docker-compose restart backend

# Or restart all services
docker-compose down && docker-compose up -d
```

### 2. Voice Recognition Not Working

#### Symptoms
- Microphone button not responding
- "Voice recognition not supported" message
- Audio not being transcribed

#### Possible Causes
- Browser permissions
- Microphone hardware issues
- Browser compatibility
- HTTPS requirement

#### Solutions

**Check Browser Permissions**
1. Click the lock icon in the address bar
2. Ensure microphone is set to "Allow"
3. Refresh the page

**Verify HTTPS**
```javascript
// Voice recognition requires HTTPS
if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
  console.error('Voice recognition requires HTTPS');
}
```

**Test Microphone**
```javascript
// Test microphone access
navigator.mediaDevices.getUserMedia({ audio: true })
  .then(stream => {
    console.log('Microphone access granted');
    stream.getTracks().forEach(track => track.stop());
  })
  .catch(err => console.error('Microphone access denied:', err));
```

**Browser Compatibility**
- Chrome 25+
- Firefox 44+
- Safari 14.1+
- Edge 79+

### 3. WebSocket Connection Issues

#### Symptoms
- Real-time features not working
- Connection timeouts
- Frequent disconnections

#### Possible Causes
- Proxy/firewall blocking WebSocket
- Network instability
- Server overload
- Authentication issues

#### Solutions

**Check WebSocket Connection**
```javascript
// Test WebSocket connection
const ws = new WebSocket('ws://localhost:8000/api/v1/copilotkit/ws/test-user');
ws.onopen = () => console.log('WebSocket connected');
ws.onerror = (error) => console.error('WebSocket error:', error);
ws.onclose = (event) => console.log('WebSocket closed:', event.code, event.reason);
```

**Verify Proxy Configuration**
```nginx
# Nginx WebSocket proxy configuration
location /ws {
    proxy_pass http://backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_read_timeout 86400;
}
```

**Check Authentication**
```bash
# Verify JWT token
curl -H "Authorization: Bearer $JWT_TOKEN" \
     http://localhost:8000/api/v1/copilotkit/health
```

### 4. Database Connection Problems

#### Symptoms
- "Database connection failed" errors
- Slow query performance
- Connection pool exhaustion

#### Possible Causes
- Incorrect connection string
- Database server down
- Connection pool limits
- Network issues

#### Solutions

**Verify Connection String**
```bash
# Test database connection
psql "$DATABASE_URL" -c "SELECT version();"
```

**Check Connection Pool**
```python
# Monitor connection pool
from app.database import engine
pool = engine.pool
print(f"Pool size: {pool.size()}")
print(f"Checked out: {pool.checkedout()}")
print(f"Overflow: {pool.overflow()}")
```

**Database Performance**
```sql
-- Check active connections
SELECT count(*) FROM pg_stat_activity;

-- Check slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
```

### 5. Performance Issues

#### Symptoms
- Slow page loading
- High response times
- Memory usage growing
- CPU spikes

#### Possible Causes
- Memory leaks
- Inefficient queries
- Large payload sizes
- Resource constraints

#### Solutions

**Monitor Resource Usage**
```bash
# Check system resources
htop
free -h
df -h

# Check Docker container resources
docker stats
```

**Analyze Performance**
```bash
# Check application logs for slow requests
grep "slow" /var/log/copilotkit/backend.log

# Monitor database performance
tail -f /var/log/postgresql/postgresql.log | grep "duration"
```

**Optimize Queries**
```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;
SELECT pg_reload_conf();

-- Analyze slow queries
EXPLAIN ANALYZE SELECT * FROM conversations WHERE user_id = 'user123';
```

### 6. Authentication and Authorization Issues

#### Symptoms
- "Unauthorized" errors
- Permission denied messages
- Login failures

#### Possible Causes
- Expired tokens
- Incorrect permissions
- Configuration issues
- Session problems

#### Solutions

**Verify JWT Token**
```bash
# Decode JWT token (without verification)
echo "$JWT_TOKEN" | cut -d. -f2 | base64 -d | jq .
```

**Check User Permissions**
```python
# Verify user permissions
from app.auth.auth_manager import get_user_permissions
permissions = get_user_permissions(user_id, tenant_id)
print(f"User permissions: {permissions}")
```

**Reset Authentication**
```bash
# Clear authentication cache
redis-cli FLUSHDB

# Restart authentication service
docker-compose restart backend
```

### 7. Report Generation Failures

#### Symptoms
- Reports not generating
- Incomplete reports
- Generation timeouts

#### Possible Causes
- Insufficient data
- Service timeouts
- Memory limitations
- API rate limits

#### Solutions

**Check Data Availability**
```sql
-- Verify data exists for the requested period
SELECT COUNT(*) FROM transactions 
WHERE created_at >= '2024-01-01' 
AND created_at < '2024-02-01';
```

**Monitor Report Generation**
```bash
# Check report service logs
docker-compose logs backend | grep "report"

# Monitor memory usage during generation
watch -n 1 'docker stats --no-stream | grep backend'
```

**Increase Timeouts**
```python
# Increase report generation timeout
REPORT_GENERATION_TIMEOUT = 300  # 5 minutes
```

## Error Codes Reference

### HTTP Status Codes

| Code | Meaning | Common Causes | Solutions |
|------|---------|---------------|-----------|
| 400 | Bad Request | Invalid parameters | Check request format |
| 401 | Unauthorized | Missing/invalid token | Refresh authentication |
| 403 | Forbidden | Insufficient permissions | Check user roles |
| 404 | Not Found | Resource doesn't exist | Verify resource ID |
| 429 | Too Many Requests | Rate limit exceeded | Implement backoff |
| 500 | Internal Server Error | Server-side error | Check logs |
| 502 | Bad Gateway | Proxy/upstream error | Check service health |
| 503 | Service Unavailable | Service overloaded | Scale resources |

### Application Error Codes

| Code | Description | Resolution |
|------|-------------|------------|
| AI_SERVICE_UNAVAILABLE | AI service not responding | Check API keys and connectivity |
| VOICE_NOT_SUPPORTED | Voice recognition unavailable | Use supported browser with HTTPS |
| WEBSOCKET_CONNECTION_FAILED | WebSocket connection error | Check proxy configuration |
| DATABASE_CONNECTION_ERROR | Database connectivity issue | Verify connection string |
| REPORT_GENERATION_TIMEOUT | Report took too long | Reduce scope or increase timeout |
| INSUFFICIENT_PERMISSIONS | User lacks required permissions | Update user roles |

## Monitoring and Alerting

### Key Metrics to Monitor

**Application Metrics**
- Response times
- Error rates
- Request volume
- Active users

**System Metrics**
- CPU usage
- Memory usage
- Disk space
- Network I/O

**Database Metrics**
- Connection count
- Query performance
- Lock waits
- Replication lag

### Setting Up Alerts

**Prometheus Alerts**
```yaml
groups:
- name: copilotkit-alerts
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    annotations:
      summary: High error rate detected

  - alert: DatabaseConnectionHigh
    expr: pg_stat_activity_count > 80
    for: 2m
    annotations:
      summary: High database connection count
```

## Log Analysis

### Important Log Locations

```bash
# Application logs
/var/log/copilotkit/backend.log
/var/log/copilotkit/frontend.log

# System logs
/var/log/syslog
/var/log/nginx/access.log
/var/log/nginx/error.log

# Database logs
/var/log/postgresql/postgresql.log
```

### Log Analysis Commands

```bash
# Find errors in the last hour
grep -i error /var/log/copilotkit/backend.log | grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')"

# Count error types
grep -i error /var/log/copilotkit/backend.log | awk '{print $5}' | sort | uniq -c

# Monitor logs in real-time
tail -f /var/log/copilotkit/backend.log | grep -i error
```

## Getting Help

### Self-Service Resources

1. **Documentation**: https://docs.bitebase.com/copilotkit
2. **API Reference**: https://docs.bitebase.com/api
3. **Community Forum**: https://community.bitebase.com
4. **Status Page**: https://status.bitebase.com

### Support Channels

**Technical Support**
- Email: <EMAIL>
- Response time: 4-24 hours
- Include: error messages, logs, steps to reproduce

**Emergency Support**
- Phone: 1-800-BITEBASE
- Available: 24/7 for critical issues
- For: service outages, security incidents

**Developer Support**
- Email: <EMAIL>
- Slack: #bitebase-developers
- For: integration questions, API issues

### Information to Include

When contacting support, please provide:

1. **Environment Details**
   - Deployment type (Docker, Kubernetes, etc.)
   - Version numbers
   - Operating system

2. **Error Information**
   - Error messages
   - Stack traces
   - Relevant log entries

3. **Reproduction Steps**
   - What you were trying to do
   - Steps to reproduce the issue
   - Expected vs. actual behavior

4. **System Information**
   - Resource usage
   - Network configuration
   - Recent changes

## Preventive Measures

### Regular Maintenance

1. **Update Dependencies**
   ```bash
   # Update Docker images
   docker-compose pull
   docker-compose up -d
   
   # Update npm packages
   npm update
   
   # Update Python packages
   pip install -r requirements.txt --upgrade
   ```

2. **Database Maintenance**
   ```sql
   -- Vacuum and analyze tables
   VACUUM ANALYZE;
   
   -- Update statistics
   ANALYZE;
   
   -- Check for bloat
   SELECT schemaname, tablename, attname, n_distinct, correlation 
   FROM pg_stats WHERE tablename = 'conversations';
   ```

3. **Log Rotation**
   ```bash
   # Configure logrotate
   /var/log/copilotkit/*.log {
       daily
       rotate 30
       compress
       delaycompress
       missingok
       notifempty
   }
   ```

### Health Checks

Implement regular health checks:

```bash
#!/bin/bash
# health-check.sh

# Check services
curl -f http://localhost:3000/health || exit 1
curl -f http://localhost:8000/health || exit 1

# Check database
psql "$DATABASE_URL" -c "SELECT 1;" || exit 1

# Check Redis
redis-cli ping || exit 1

echo "All services healthy"
```

### Backup Verification

```bash
#!/bin/bash
# verify-backup.sh

# Test database backup restore
pg_restore --list backup.sql > /dev/null || exit 1

# Verify backup integrity
md5sum backup.sql > backup.md5
md5sum -c backup.md5 || exit 1

echo "Backup verification successful"
```
