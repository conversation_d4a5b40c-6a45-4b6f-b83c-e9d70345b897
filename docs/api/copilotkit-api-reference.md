# CopilotKit API Reference

## Overview

The BiteBase CopilotKit Integration provides a comprehensive REST API for AI-powered restaurant intelligence. This API enables real-time chat interactions, location analysis, report generation, and analytics tracking.

## Base URL

```
Production: https://api.bitebase.com/api/v1/copilotkit
Development: http://localhost:8000/api/v1/copilotkit
```

## Authentication

All API endpoints require authentication using JWT tokens:

```http
Authorization: Bearer <your-jwt-token>
```

## Rate Limiting

- **Standard Plan**: 1000 requests per hour
- **Premium Plan**: 5000 requests per hour
- **Enterprise Plan**: Unlimited

Rate limit headers are included in all responses:
- `X-RateLimit-Limit`: Request limit per hour
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (Unix timestamp)

## Endpoints

### Health Check

#### GET /health

Check the health status of the CopilotKit service.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "ai_service": "healthy",
    "location_service": "healthy",
    "analytics_service": "healthy"
  }
}
```

### Chat Completion

#### POST /chat/completion

Generate AI responses for user queries.

**Request Body:**
```json
{
  "message": "What's my revenue for last month?",
  "context": {
    "tenant_id": "restaurant-123",
    "user_id": "user-456",
    "conversation_id": "conv-789"
  },
  "model": "gpt-4",
  "temperature": 0.7,
  "max_tokens": 1000
}
```

**Response:**
```json
{
  "response": "Based on your data, last month's revenue was $45,230, representing a 12% increase from the previous month.",
  "model": "gpt-4",
  "usage": {
    "tokens": 156,
    "cost": 0.0234
  },
  "conversation_id": "conv-789",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Missing or invalid authentication
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Service unavailable

### Streaming Chat

#### POST /chat/stream

Stream AI responses in real-time using Server-Sent Events.

**Request Body:**
```json
{
  "message": "Generate a detailed market analysis",
  "context": {
    "tenant_id": "restaurant-123"
  },
  "stream": true
}
```

**Response (Server-Sent Events):**
```
data: {"delta": "Based", "type": "text"}

data: {"delta": " on your", "type": "text"}

data: {"delta": " market data", "type": "text"}

data: {"type": "done", "usage": {"tokens": 245}}
```

### Location Analysis

#### POST /location/analyze

Analyze a location for restaurant viability.

**Request Body:**
```json
{
  "address": "123 Main Street, New York, NY 10001",
  "radius": 1.0,
  "analysis_type": "comprehensive",
  "restaurant_type": "casual_dining"
}
```

**Response:**
```json
{
  "location_id": "loc-abc123",
  "coordinates": {
    "lat": 40.7128,
    "lng": -74.0060
  },
  "score": 85.5,
  "demographics": {
    "population": 50000,
    "median_income": 65000,
    "age_groups": {
      "18-34": 35,
      "35-54": 40,
      "55+": 25
    }
  },
  "competition": {
    "nearby_restaurants": 15,
    "direct_competitors": 3,
    "market_saturation": "medium",
    "avg_rating": 4.2
  },
  "foot_traffic": {
    "weekday_avg": 1200,
    "weekend_avg": 1800,
    "peak_hours": ["12:00-14:00", "18:00-20:00"]
  },
  "recommendations": [
    "Consider lunch specials for office workers",
    "Target family dining for evening hours",
    "Implement delivery services for convenience"
  ],
  "analysis_date": "2024-01-15T10:30:00Z"
}
```

### Report Generation

#### POST /reports/generate

Generate comprehensive business reports.

**Request Body:**
```json
{
  "report_type": "market_analysis",
  "parameters": {
    "time_period": "last_quarter",
    "include_forecasts": true,
    "include_competitors": true,
    "format": "detailed"
  }
}
```

**Response:**
```json
{
  "report_id": "report-xyz789",
  "type": "market_analysis",
  "status": "completed",
  "data": {
    "executive_summary": "Your restaurant shows strong performance with 15% revenue growth...",
    "key_findings": [
      "Revenue increased 15% year-over-year",
      "Customer satisfaction improved to 4.2/5",
      "Peak hours shifted to earlier evening times"
    ],
    "financial_metrics": {
      "revenue": 125000,
      "profit_margin": 18.5,
      "avg_order_value": 32.50
    },
    "recommendations": [
      "Expand delivery services to capture growing market",
      "Introduce loyalty program to increase retention",
      "Optimize menu pricing based on demand patterns"
    ],
    "forecasts": {
      "next_quarter_revenue": 140000,
      "growth_rate": 12
    }
  },
  "generated_at": "2024-01-15T10:30:00Z",
  "expires_at": "2024-01-22T10:30:00Z"
}
```

**Report Types:**
- `market_analysis`: Comprehensive market and performance analysis
- `competitive_analysis`: Competitor landscape and positioning
- `financial_summary`: Financial performance and trends
- `location_analysis`: Location-specific insights and recommendations

### Analytics & Metrics

#### GET /metrics

Retrieve real-time performance metrics.

**Query Parameters:**
- `period`: Time period (1h, 24h, 7d, 30d)
- `metrics`: Comma-separated list of specific metrics

**Response:**
```json
{
  "performance": {
    "avg_response_time": 1.2,
    "success_rate": 99.5,
    "error_rate": 0.5
  },
  "usage": {
    "total_requests": 1250,
    "unique_users": 45,
    "popular_actions": [
      "chat_completion",
      "location_analysis",
      "report_generation"
    ]
  },
  "system_health": {
    "cpu_usage": 65,
    "memory_usage": 78,
    "disk_usage": 45
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### GET /analytics

Retrieve detailed analytics data.

**Query Parameters:**
- `period`: Analysis period (7d, 30d, 90d)
- `breakdown`: Breakdown type (daily, weekly, monthly)

**Response:**
```json
{
  "usage_stats": {
    "total_queries": 5420,
    "unique_users": 234,
    "avg_session_duration": 8.5,
    "popular_features": [
      {"feature": "chat_completion", "usage": 3200},
      {"feature": "location_analysis", "usage": 1100},
      {"feature": "report_generation", "usage": 1120}
    ]
  },
  "performance_trends": {
    "response_times": [
      {"date": "2024-01-08", "avg_time": 1.1},
      {"date": "2024-01-09", "avg_time": 1.3},
      {"date": "2024-01-10", "avg_time": 1.2}
    ],
    "success_rates": [
      {"date": "2024-01-08", "rate": 99.2},
      {"date": "2024-01-09", "rate": 99.8},
      {"date": "2024-01-10", "rate": 99.5}
    ]
  },
  "user_engagement": {
    "daily_active_users": 156,
    "retention_rate": 78.5,
    "feature_adoption": {
      "voice_recognition": 45,
      "streaming_responses": 89,
      "report_downloads": 67
    }
  }
}
```

## WebSocket API

### Connection

Connect to real-time WebSocket endpoint:

```
ws://localhost:8000/api/v1/copilotkit/ws/{user_id}
```

### Message Format

**Client to Server:**
```json
{
  "type": "chat_message",
  "content": "What's my best-selling item?",
  "context": {
    "tenant_id": "restaurant-123",
    "conversation_id": "conv-456"
  }
}
```

**Server to Client:**
```json
{
  "type": "ai_response",
  "content": "Your best-selling item is the Classic Burger with 234 orders this month.",
  "conversation_id": "conv-456",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Message Types

- `chat_message`: User chat input
- `ai_response`: AI assistant response
- `typing_indicator`: Show typing status
- `error`: Error message
- `connection_established`: Connection confirmation
- `heartbeat`: Keep-alive ping

## Error Handling

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The request parameters are invalid",
    "details": {
      "field": "message",
      "issue": "Message cannot be empty"
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req-abc123"
  }
}
```

### Error Codes

- `INVALID_REQUEST`: Request validation failed
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `RATE_LIMITED`: Rate limit exceeded
- `SERVICE_UNAVAILABLE`: External service error
- `INTERNAL_ERROR`: Server error

## SDKs and Libraries

### JavaScript/TypeScript

```bash
npm install @bitebase/copilotkit-sdk
```

```typescript
import { CopilotKitClient } from '@bitebase/copilotkit-sdk';

const client = new CopilotKitClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.bitebase.com'
});

const response = await client.chat.completion({
  message: 'What is my revenue?',
  context: { tenant_id: 'restaurant-123' }
});
```

### Python

```bash
pip install bitebase-copilotkit
```

```python
from bitebase_copilotkit import CopilotKitClient

client = CopilotKitClient(
    api_key='your-api-key',
    base_url='https://api.bitebase.com'
)

response = client.chat.completion(
    message='What is my revenue?',
    context={'tenant_id': 'restaurant-123'}
)
```

## Webhooks

Configure webhooks to receive real-time notifications:

### Webhook Events

- `report.completed`: Report generation finished
- `analysis.completed`: Location analysis finished
- `alert.triggered`: System alert triggered
- `usage.threshold`: Usage threshold reached

### Webhook Payload

```json
{
  "event": "report.completed",
  "data": {
    "report_id": "report-xyz789",
    "tenant_id": "restaurant-123",
    "type": "market_analysis",
    "status": "completed"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "signature": "sha256=abc123..."
}
```

## Support

- **Documentation**: https://docs.bitebase.com/copilotkit
- **API Status**: https://status.bitebase.com
- **Support Email**: <EMAIL>
- **Developer Forum**: https://community.bitebase.com
