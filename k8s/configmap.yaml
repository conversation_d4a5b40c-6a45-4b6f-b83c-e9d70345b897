apiVersion: v1
kind: ConfigMap
metadata:
  name: copilotkit-config
  namespace: bitebase-copilotkit
  labels:
    app: copilotkit
data:
  # Environment configuration
  NODE_ENV: "production"
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # Feature flags
  ENABLE_COPILOTKIT_SECURITY: "true"
  ENABLE_COPILOTKIT_MONITORING: "true"
  ENABLE_RATE_LIMITING: "true"
  ENABLE_VOICE_RECOGNITION: "true"
  ENABLE_ANALYTICS: "true"
  
  # Database configuration
  POSTGRES_DB: "bitebase_copilotkit"
  POSTGRES_USER: "copilotkit_user"
  
  # Redis configuration
  REDIS_URL: "redis://redis-service:6379"
  
  # API configuration
  API_VERSION: "v1"
  API_PREFIX: "/api/v1"
  
  # Monitoring configuration
  PROMETHEUS_ENABLED: "true"
  METRICS_PORT: "9090"
  
  # Security configuration
  CORS_ORIGINS: "https://app.bitebase.com,https://admin.bitebase.com"
  ALLOWED_HOSTS: "api.bitebase.com,localhost"
  
  # Performance configuration
  MAX_WORKERS: "4"
  WORKER_TIMEOUT: "300"
  KEEP_ALIVE: "2"
  
  # Cache configuration
  CACHE_TTL: "3600"
  SESSION_TIMEOUT: "86400"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: bitebase-copilotkit
  labels:
    app: nginx
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 100M;

        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        upstream frontend {
            server frontend-service:3000;
        }

        upstream backend {
            server backend-service:8000;
        }

        server {
            listen 80;
            server_name _;
            return 301 https://$host$request_uri;
        }

        server {
            listen 443 ssl http2;
            server_name app.bitebase.com;

            ssl_certificate /etc/nginx/ssl/cert.pem;
            ssl_certificate_key /etc/nginx/ssl/key.pem;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
            ssl_prefer_server_ciphers off;

            location / {
                proxy_pass http://frontend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
            }

            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
        }

        server {
            listen 443 ssl http2;
            server_name api.bitebase.com;

            ssl_certificate /etc/nginx/ssl/cert.pem;
            ssl_certificate_key /etc/nginx/ssl/key.pem;

            location / {
                proxy_pass http://backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_read_timeout 300s;
                proxy_connect_timeout 75s;
            }

            location /ws {
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_read_timeout 86400;
            }
        }
    }
