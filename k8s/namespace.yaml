apiVersion: v1
kind: Namespace
metadata:
  name: bitebase-copilotkit
  labels:
    name: bitebase-copilotkit
    app: copilotkit
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: copilotkit-quota
  namespace: bitebase-copilotkit
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: copilotkit-limits
  namespace: bitebase-copilotkit
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
