apiVersion: v1
kind: Secret
metadata:
  name: copilotkit-secrets
  namespace: bitebase-copilotkit
  labels:
    app: copilotkit
type: Opaque
data:
  # Database credentials (base64 encoded)
  POSTGRES_PASSWORD: cGFzc3dvcmQxMjM=  # password123
  DATABASE_URL: ****************************************************************************************************************
  
  # JWT secrets
  JWT_SECRET: eW91ci1qd3Qtc2VjcmV0LWtleS1oZXJl  # your-jwt-secret-key-here
  JWT_REFRESH_SECRET: eW91ci1qd3QtcmVmcmVzaC1zZWNyZXQta2V5LWhlcmU=  # your-jwt-refresh-secret-key-here
  ENCRYPTION_KEY: eW91ci1lbmNyeXB0aW9uLWtleS1oZXJl  # your-encryption-key-here
  
  # AI API keys (replace with actual base64 encoded keys)
  OPENAI_API_KEY: c2steW91ci1vcGVuYWkta2V5LWhlcmU=  # sk-your-openai-key-here
  ANTHROPIC_API_KEY: eW91ci1hbnRocm9waWMta2V5LWhlcmU=  # your-anthropic-key-here
  LANGCHAIN_API_KEY: eW91ci1sYW5nY2hhaW4ta2V5LWhlcmU=  # your-langchain-key-here
  
  # External service API keys
  GOOGLE_MAPS_API_KEY: eW91ci1nb29nbGUtbWFwcy1rZXktaGVyZQ==  # your-google-maps-key-here
  SENDGRID_API_KEY: eW91ci1zZW5kZ3JpZC1rZXktaGVyZQ==  # your-sendgrid-key-here
  
  # OAuth credentials
  GOOGLE_CLIENT_ID: eW91ci1nb29nbGUtY2xpZW50LWlkLWhlcmU=  # your-google-client-id-here
  GOOGLE_CLIENT_SECRET: eW91ci1nb29nbGUtY2xpZW50LXNlY3JldC1oZXJl  # your-google-client-secret-here
  
  # Monitoring credentials
  GRAFANA_ADMIN_PASSWORD: YWRtaW4xMjM=  # admin123
  PROMETHEUS_PASSWORD: cHJvbWV0aGV1czEyMw==  # prometheus123
---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: bitebase-copilotkit
  labels:
    app: copilotkit
type: kubernetes.io/tls
data:
  # TLS certificate and key (base64 encoded)
  # Replace with actual certificate data
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...
---
apiVersion: v1
kind: Secret
metadata:
  name: registry-secret
  namespace: bitebase-copilotkit
  labels:
    app: copilotkit
type: kubernetes.io/dockerconfigjson
data:
  # Docker registry credentials for private images
  .dockerconfigjson: ****************************************************************************************************************************************************************************************
