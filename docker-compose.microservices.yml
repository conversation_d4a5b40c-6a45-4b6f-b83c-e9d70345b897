# BiteBase Intelligence - Microservices Architecture
# Docker Compose configuration for development and testing
version: '3.8'

# Shared networks
networks:
  bitebase-microservices:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Shared volumes
volumes:
  postgres_main_data:
    driver: local
  postgres_analytics_data:
    driver: local
  postgres_location_data:
    driver: local
  postgres_auth_data:
    driver: local
  redis_data:
    driver: local
  vector_db_data:
    driver: local
  rabbitmq_data:
    driver: local
  kong_data:
    driver: local

services:
  # ===== INFRASTRUCTURE SERVICES =====
  
  # API Gateway (Kong)
  kong:
    image: kong:3.4-alpine
    container_name: bitebase-kong
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_ADMIN_GUI_URL: http://localhost:8002
    volumes:
      - ./kong/kong.yml:/kong/kong.yml:ro
      - kong_data:/var/lib/kong/data
    ports:
      - "8000:8000"  # Proxy port
      - "8001:8001"  # Admin API
      - "8002:8002"  # Admin GUI
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Message Queue (RabbitMQ)
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: bitebase-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: bitebase
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-rabbitmq_dev_password}
      RABBITMQ_DEFAULT_VHOST: bitebase
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: bitebase-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_dev_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Vector Database (Qdrant)
  vector-db:
    image: qdrant/qdrant:v1.7.0
    container_name: bitebase-qdrant
    volumes:
      - vector_db_data:/qdrant/storage
    ports:
      - "6333:6333"
      - "6334:6334"
    networks:
      - bitebase-microservices
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    restart: unless-stopped

  # ===== DATABASE SERVICES =====

  # Main Database (Restaurant data)
  postgres-main:
    image: postgres:15-alpine
    container_name: bitebase-postgres-main
    environment:
      POSTGRES_DB: restaurant_db
      POSTGRES_USER: restaurant_user
      POSTGRES_PASSWORD: ${MAIN_DB_PASSWORD:-main_db_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_main_data:/var/lib/postgresql/data
      - ./scripts/init-main-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U restaurant_user -d restaurant_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Analytics Database (TimescaleDB)
  postgres-analytics:
    image: timescale/timescaledb:2.13.0-pg15
    container_name: bitebase-timescaledb
    environment:
      POSTGRES_DB: analytics_db
      POSTGRES_USER: analytics_user
      POSTGRES_PASSWORD: ${ANALYTICS_DB_PASSWORD:-analytics_db_password}
    volumes:
      - postgres_analytics_data:/var/lib/postgresql/data
      - ./scripts/init-analytics-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U analytics_user -d analytics_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Location Database (PostGIS)
  postgres-location:
    image: postgis/postgis:15-3.4
    container_name: bitebase-postgis
    environment:
      POSTGRES_DB: location_db
      POSTGRES_USER: location_user
      POSTGRES_PASSWORD: ${LOCATION_DB_PASSWORD:-location_db_password}
    volumes:
      - postgres_location_data:/var/lib/postgresql/data
      - ./scripts/init-location-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5434:5432"
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U location_user -d location_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Auth Database
  postgres-auth:
    image: postgres:15-alpine
    container_name: bitebase-postgres-auth
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: ${AUTH_DB_PASSWORD:-auth_db_password}
    volumes:
      - postgres_auth_data:/var/lib/postgresql/data
      - ./scripts/init-auth-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5435:5432"
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auth_user -d auth_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # ===== MICROSERVICES =====

  # Authentication Service
  auth-service:
    build:
      context: ./services/auth
      dockerfile: Dockerfile
    container_name: bitebase-auth-service
    environment:
      - DATABASE_URL=postgresql://auth_user:${AUTH_DB_PASSWORD:-auth_db_password}@postgres-auth:5432/auth_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/1
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-auth_jwt_secret_key}
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      - SERVICE_NAME=auth-service
      - LOG_LEVEL=INFO
    volumes:
      - ./services/auth:/app
    depends_on:
      postgres-auth:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # AI Intelligence Service
  ai-service:
    build:
      context: ./services/ai
      dockerfile: Dockerfile
    container_name: bitebase-ai-service
    environment:
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/2
      - VECTOR_DB_URL=http://vector-db:6333
      - RABBITMQ_URL=amqp://bitebase:${RABBITMQ_PASSWORD:-rabbitmq_dev_password}@rabbitmq:5672/bitebase
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - SERVICE_NAME=ai-service
      - LOG_LEVEL=INFO
    volumes:
      - ./services/ai:/app
    depends_on:
      redis:
        condition: service_healthy
      vector-db:
        condition: service_started
      rabbitmq:
        condition: service_healthy
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Analytics Service
  analytics-service:
    build:
      context: ./services/analytics
      dockerfile: Dockerfile
    container_name: bitebase-analytics-service
    environment:
      - DATABASE_URL=postgresql://analytics_user:${ANALYTICS_DB_PASSWORD:-analytics_db_password}@postgres-analytics:5432/analytics_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/3
      - RABBITMQ_URL=amqp://bitebase:${RABBITMQ_PASSWORD:-rabbitmq_dev_password}@rabbitmq:5672/bitebase
      - SERVICE_NAME=analytics-service
      - LOG_LEVEL=INFO
    volumes:
      - ./services/analytics:/app
    depends_on:
      postgres-analytics:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Location Intelligence Service
  location-service:
    build:
      context: ./services/location
      dockerfile: Dockerfile
    container_name: bitebase-location-service
    environment:
      - DATABASE_URL=postgresql://location_user:${LOCATION_DB_PASSWORD:-location_db_password}@postgres-location:5432/location_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/4
      - RABBITMQ_URL=amqp://bitebase:${RABBITMQ_PASSWORD:-rabbitmq_dev_password}@rabbitmq:5672/bitebase
      - GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
      - MAPBOX_ACCESS_TOKEN=${MAPBOX_ACCESS_TOKEN}
      - SERVICE_NAME=location-service
      - LOG_LEVEL=INFO
    volumes:
      - ./services/location:/app
    depends_on:
      postgres-location:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Restaurant Management Service
  restaurant-service:
    build:
      context: ./services/restaurant
      dockerfile: Dockerfile
    container_name: bitebase-restaurant-service
    environment:
      - DATABASE_URL=postgresql://restaurant_user:${MAIN_DB_PASSWORD:-main_db_password}@postgres-main:5432/restaurant_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/5
      - RABBITMQ_URL=amqp://bitebase:${RABBITMQ_PASSWORD:-rabbitmq_dev_password}@rabbitmq:5672/bitebase
      - SERVICE_NAME=restaurant-service
      - LOG_LEVEL=INFO
    volumes:
      - ./services/restaurant:/app
    depends_on:
      postgres-main:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Reports Service
  reports-service:
    build:
      context: ./services/reports
      dockerfile: Dockerfile
    container_name: bitebase-reports-service
    environment:
      - DATABASE_URL=postgresql://restaurant_user:${MAIN_DB_PASSWORD:-main_db_password}@postgres-main:5432/restaurant_db
      - ANALYTICS_DB_URL=postgresql://analytics_user:${ANALYTICS_DB_PASSWORD:-analytics_db_password}@postgres-analytics:5432/analytics_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/6
      - RABBITMQ_URL=amqp://bitebase:${RABBITMQ_PASSWORD:-rabbitmq_dev_password}@rabbitmq:5672/bitebase
      - S3_BUCKET_NAME=${S3_BUCKET_NAME:-bitebase-reports}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - SERVICE_NAME=reports-service
      - LOG_LEVEL=INFO
    volumes:
      - ./services/reports:/app
      - ./shared/reports:/app/generated_reports
    depends_on:
      postgres-main:
        condition: service_healthy
      postgres-analytics:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - bitebase-microservices
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # ===== MONITORING & OBSERVABILITY =====

  # Prometheus
  prometheus:
    image: prom/prometheus:v2.48.0
    container_name: bitebase-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/alerts:/etc/prometheus/alerts
    ports:
      - "9090:9090"
    networks:
      - bitebase-microservices
    restart: unless-stopped

  # Grafana
  grafana:
    image: grafana/grafana:10.2.0
    container_name: bitebase-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    volumes:
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"
    networks:
      - bitebase-microservices
    depends_on:
      - prometheus
    restart: unless-stopped

  # Jaeger (Distributed Tracing)
  jaeger:
    image: jaegertracing/all-in-one:1.51
    container_name: bitebase-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
      - "6831:6831/udp"  # Jaeger agent UDP
    networks:
      - bitebase-microservices
    restart: unless-stopped

  # Elasticsearch (Logging)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: bitebase-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    ports:
      - "9200:9200"
    networks:
      - bitebase-microservices
    restart: unless-stopped
    profiles:
      - logging

  # Kibana (Log Visualization)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: bitebase-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - bitebase-microservices
    depends_on:
      - elasticsearch
    restart: unless-stopped
    profiles:
      - logging

  # ===== FRONTEND SERVICE =====

  # Frontend (Next.js)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.microservices
    container_name: bitebase-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://kong:8000
      - NEXT_PUBLIC_WS_URL=ws://kong:8000/ws
      - NEXT_PUBLIC_ENVIRONMENT=microservices
      - NEXT_PUBLIC_APP_VERSION=2.1.0-microservices
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    networks:
      - bitebase-microservices
    depends_on:
      - kong
    restart: unless-stopped

# Development overrides (use with docker compose -f docker-compose.microservices.yml -f docker-compose.dev.yml)
# This allows for volume mounting for live development
x-development-overrides: &dev-overrides
  volumes:
    - ./services/auth:/app
    - /app/__pycache__

# Production overrides (use with docker compose -f docker-compose.microservices.yml -f docker-compose.prod.yml)
x-production-overrides: &prod-overrides
  restart: always
  deploy:
    replicas: 3
    resources:
      limits:
        memory: 1G
        cpus: '0.5'
      reservations:
        memory: 512M
        cpus: '0.25'