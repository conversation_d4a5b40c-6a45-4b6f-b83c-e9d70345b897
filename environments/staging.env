# BiteBase CopilotKit Staging Environment Configuration

# Application Environment
NODE_ENV=staging
ENVIRONMENT=staging
DEBUG=true
LOG_LEVEL=DEBUG

# API Configuration
API_VERSION=v1
API_PREFIX=/api/v1
MAX_REQUEST_SIZE=100MB
REQUEST_TIMEOUT=300

# Database Configuration
POSTGRES_DB=bitebase_copilotkit_staging
POSTGRES_USER=copilotkit_staging_user
# POSTGRES_PASSWORD=<set-in-secrets>
# DATABASE_URL=<set-in-secrets>
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Redis Configuration
REDIS_URL=redis://redis-service:6379
REDIS_DB=1
REDIS_MAX_CONNECTIONS=25
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# AI Service Configuration
# OPENAI_API_KEY=<set-in-secrets>
# ANTHROPIC_API_KEY=<set-in-secrets>
# LANGCHAIN_API_KEY=<set-in-secrets>
AI_MODEL_DEFAULT=gpt-3.5-turbo
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=1500
AI_REQUEST_TIMEOUT=60

# External APIs
# GOOGLE_MAPS_API_KEY=<set-in-secrets>
# SENDGRID_API_KEY=<set-in-secrets>
GOOGLE_MAPS_REGION=US
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=BiteBase CopilotKit Staging

# Security Configuration
# JWT_SECRET=<set-in-secrets>
# JWT_REFRESH_SECRET=<set-in-secrets>
# ENCRYPTION_KEY=<set-in-secrets>
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_MIN_LENGTH=6
SESSION_TIMEOUT=86400

# Feature Flags
ENABLE_COPILOTKIT_SECURITY=true
ENABLE_COPILOTKIT_MONITORING=true
ENABLE_RATE_LIMITING=true
ENABLE_VOICE_RECOGNITION=true
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_COMPRESSION=true
ENABLE_CORS=true

# Rate Limiting (more lenient for testing)
RATE_LIMIT_REQUESTS_PER_MINUTE=200
RATE_LIMIT_BURST=400
RATE_LIMIT_WINDOW=60

# Monitoring and Observability
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_RETENTION_DAYS=7

# Performance Configuration
WORKER_PROCESSES=2
WORKER_CONNECTIONS=500
WORKER_TIMEOUT=300
KEEP_ALIVE_TIMEOUT=2
MAX_CONCURRENT_REQUESTS=500
CACHE_TTL=1800
STATIC_FILE_CACHE_TTL=3600

# CORS Configuration (more permissive for testing)
CORS_ORIGINS=https://staging-app.bitebase.com,https://staging-admin.bitebase.com,http://localhost:3000
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_HEADERS=Content-Type,Authorization,X-Requested-With,X-Test-Header
CORS_CREDENTIALS=true

# SSL/TLS Configuration
SSL_ENABLED=true
SSL_REDIRECT=true
HSTS_MAX_AGE=3600
HSTS_INCLUDE_SUBDOMAINS=false

# File Upload Configuration
UPLOAD_MAX_SIZE=25MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/csv,text/plain
UPLOAD_PATH=/app/uploads
UPLOAD_TEMP_PATH=/tmp/uploads

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 4 * * *
BACKUP_RETENTION_DAYS=7
BACKUP_COMPRESSION=true

# Notification Configuration
SLACK_WEBHOOK_ENABLED=true
EMAIL_NOTIFICATIONS_ENABLED=true
ALERT_THRESHOLD_ERROR_RATE=10
ALERT_THRESHOLD_RESPONSE_TIME=10000

# Scaling Configuration
AUTO_SCALING_ENABLED=false
MIN_REPLICAS=2
MAX_REPLICAS=5
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85

# Content Security Policy (more lenient for testing)
CSP_DEFAULT_SRC='self' 'unsafe-inline' 'unsafe-eval'
CSP_SCRIPT_SRC='self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net
CSP_STYLE_SRC='self' 'unsafe-inline' https://fonts.googleapis.com
CSP_FONT_SRC='self' https://fonts.gstatic.com
CSP_IMG_SRC='self' data: https:
CSP_CONNECT_SRC='self' https://api.openai.com https://api.anthropic.com ws: wss:

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_SAMPLING_RATE=0.5
ANALYTICS_BATCH_SIZE=50
ANALYTICS_FLUSH_INTERVAL=30

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=Staging environment is under maintenance.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1,10.0.0.0/8

# Timezone Configuration
TZ=UTC
DEFAULT_TIMEZONE=America/New_York

# Locale Configuration
DEFAULT_LOCALE=en_US
SUPPORTED_LOCALES=en_US,es_ES

# Cache Configuration
CACHE_BACKEND=redis
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=copilotkit:staging:
CACHE_VERSION=1

# Session Configuration
SESSION_BACKEND=redis
SESSION_COOKIE_NAME=copilotkit_staging_session
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# WebSocket Configuration
WEBSOCKET_ENABLED=true
WEBSOCKET_MAX_CONNECTIONS=500
WEBSOCKET_PING_INTERVAL=30
WEBSOCKET_PING_TIMEOUT=10

# API Documentation (enabled for testing)
API_DOCS_ENABLED=true
API_DOCS_URL=/docs
REDOC_URL=/redoc

# Development Tools (enabled for debugging)
DEBUG_TOOLBAR=true
PROFILER_ENABLED=true
SQL_DEBUG=true

# Testing Configuration
TEST_MODE=false
MOCK_EXTERNAL_APIS=false
SEED_TEST_DATA=true

# Load Testing
LOAD_TEST_ENABLED=false
LOAD_TEST_USERS=100
LOAD_TEST_DURATION=300

# Feature Testing
FEATURE_FLAG_OVERRIDE=true
A_B_TESTING_ENABLED=true
CANARY_DEPLOYMENT=false

# Data Seeding
SEED_SAMPLE_DATA=true
SEED_ADMIN_USER=true
SEED_TEST_TENANTS=true

# Monitoring Alerts (less strict for staging)
ALERT_CPU_THRESHOLD=85
ALERT_MEMORY_THRESHOLD=90
ALERT_DISK_THRESHOLD=85
ALERT_ERROR_RATE_THRESHOLD=15

# Performance Testing
PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=2000
RESPONSE_TIME_THRESHOLD=5000

# Security Testing
SECURITY_HEADERS_ENABLED=true
VULNERABILITY_SCANNING=true
PENETRATION_TESTING=false

# Integration Testing
EXTERNAL_API_TESTING=true
WEBHOOK_TESTING=true
EMAIL_TESTING=true

# Deployment Configuration
DEPLOYMENT_STRATEGY=rolling
HEALTH_CHECK_GRACE_PERIOD=60
READINESS_PROBE_DELAY=10
LIVENESS_PROBE_DELAY=30
