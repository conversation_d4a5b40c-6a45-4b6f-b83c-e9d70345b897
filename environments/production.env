# BiteBase CopilotKit Production Environment Configuration

# Application Environment
NODE_ENV=production
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# API Configuration
API_VERSION=v1
API_PREFIX=/api/v1
MAX_REQUEST_SIZE=100MB
REQUEST_TIMEOUT=300

# Database Configuration
POSTGRES_DB=bitebase_copilotkit_prod
POSTGRES_USER=copilotkit_prod_user
# POSTGRES_PASSWORD=<set-in-secrets>
# DATABASE_URL=<set-in-secrets>
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Redis Configuration
REDIS_URL=redis://redis-service:6379
REDIS_DB=0
REDIS_MAX_CONNECTIONS=50
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# AI Service Configuration
# OPENAI_API_KEY=<set-in-secrets>
# ANTHROPIC_API_KEY=<set-in-secrets>
# LANGCHAIN_API_KEY=<set-in-secrets>
AI_MODEL_DEFAULT=gpt-4
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=2000
AI_REQUEST_TIMEOUT=60

# External APIs
# GOOGLE_MAPS_API_KEY=<set-in-secrets>
# SENDGRID_API_KEY=<set-in-secrets>
GOOGLE_MAPS_REGION=US
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=BiteBase CopilotKit

# Security Configuration
# JWT_SECRET=<set-in-secrets>
# JWT_REFRESH_SECRET=<set-in-secrets>
# ENCRYPTION_KEY=<set-in-secrets>
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_MIN_LENGTH=8
SESSION_TIMEOUT=86400

# Feature Flags
ENABLE_COPILOTKIT_SECURITY=true
ENABLE_COPILOTKIT_MONITORING=true
ENABLE_RATE_LIMITING=true
ENABLE_VOICE_RECOGNITION=true
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_COMPRESSION=true
ENABLE_CORS=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=200
RATE_LIMIT_WINDOW=60

# Monitoring and Observability
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_RETENTION_DAYS=30

# Performance Configuration
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
WORKER_TIMEOUT=300
KEEP_ALIVE_TIMEOUT=2
MAX_CONCURRENT_REQUESTS=1000
CACHE_TTL=3600
STATIC_FILE_CACHE_TTL=86400

# CORS Configuration
CORS_ORIGINS=https://app.bitebase.com,https://admin.bitebase.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_CREDENTIALS=true

# SSL/TLS Configuration
SSL_ENABLED=true
SSL_REDIRECT=true
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true

# File Upload Configuration
UPLOAD_MAX_SIZE=50MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/csv
UPLOAD_PATH=/app/uploads
UPLOAD_TEMP_PATH=/tmp/uploads

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION=true

# Notification Configuration
SLACK_WEBHOOK_ENABLED=true
EMAIL_NOTIFICATIONS_ENABLED=true
ALERT_THRESHOLD_ERROR_RATE=5
ALERT_THRESHOLD_RESPONSE_TIME=5000

# Scaling Configuration
AUTO_SCALING_ENABLED=true
MIN_REPLICAS=3
MAX_REPLICAS=10
CPU_THRESHOLD=70
MEMORY_THRESHOLD=80

# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net
CSP_STYLE_SRC='self' 'unsafe-inline' https://fonts.googleapis.com
CSP_FONT_SRC='self' https://fonts.gstatic.com
CSP_IMG_SRC='self' data: https:
CSP_CONNECT_SRC='self' https://api.openai.com https://api.anthropic.com

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_SAMPLING_RATE=1.0
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=60

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1

# Timezone Configuration
TZ=UTC
DEFAULT_TIMEZONE=America/New_York

# Locale Configuration
DEFAULT_LOCALE=en_US
SUPPORTED_LOCALES=en_US,es_ES,fr_FR

# Cache Configuration
CACHE_BACKEND=redis
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=copilotkit:prod:
CACHE_VERSION=1

# Session Configuration
SESSION_BACKEND=redis
SESSION_COOKIE_NAME=copilotkit_session
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Strict

# WebSocket Configuration
WEBSOCKET_ENABLED=true
WEBSOCKET_MAX_CONNECTIONS=1000
WEBSOCKET_PING_INTERVAL=30
WEBSOCKET_PING_TIMEOUT=10

# API Documentation
API_DOCS_ENABLED=false
API_DOCS_URL=/docs
REDOC_URL=/redoc

# Development Tools (disabled in production)
DEBUG_TOOLBAR=false
PROFILER_ENABLED=false
SQL_DEBUG=false
