# Kong Declarative Configuration for BiteBase Intelligence Microservices
_format_version: "3.0"

# Services Definition
services:
  # AI Intelligence Service
  - name: ai-service
    url: http://ai-service:8000
    tags:
      - ai
      - intelligence
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3

  # Analytics Engine Service  
  - name: analytics-service
    url: http://analytics-service:8000
    tags:
      - analytics
      - reporting
    connect_timeout: 15000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3

  # Location Intelligence Service
  - name: location-service
    url: http://location-service:8000
    tags:
      - location
      - geospatial
    connect_timeout: 15000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3

  # Restaurant Management Service
  - name: restaurant-service
    url: http://restaurant-service:8000
    tags:
      - restaurant
      - management
    connect_timeout: 15000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3

  # Reports Service
  - name: reports-service
    url: http://reports-service:8000
    tags:
      - reports
      - export
    connect_timeout: 15000
    write_timeout: 120000  # Extended for report generation
    read_timeout: 120000
    retries: 2

  # Authentication Service
  - name: auth-service
    url: http://auth-service:8000
    tags:
      - auth
      - security
    connect_timeout: 10000
    write_timeout: 15000
    read_timeout: 15000
    retries: 3

# Routes Configuration
routes:
  # AI Service Routes
  - name: ai-chat
    service: ai-service
    paths:
      - /api/v1/ai/chat
    methods:
      - POST
    strip_path: true

  - name: ai-market-analysis
    service: ai-service
    paths:
      - /api/v1/ai/market-analysis
    methods:
      - POST
    strip_path: true

  - name: ai-predictions
    service: ai-service
    paths:
      - /api/v1/ai/predictions
    methods:
      - POST
    strip_path: true

  - name: ai-insights
    service: ai-service
    paths:
      - /api/v1/ai/insights
    methods:
      - GET
      - POST
    strip_path: true

  - name: nl-query
    service: ai-service
    paths:
      - /api/v1/nl-query
    methods:
      - GET
      - POST
    strip_path: true

  # Analytics Service Routes
  - name: analytics-dashboard
    service: analytics-service
    paths:
      - /api/v1/analytics/dashboard
    methods:
      - GET
    strip_path: true

  - name: analytics-performance
    service: analytics-service
    paths:
      - /api/v1/analytics/performance
    methods:
      - GET
      - POST
    strip_path: true

  - name: analytics-realtime
    service: analytics-service
    paths:
      - /api/v1/analytics/realtime
    methods:
      - GET
    strip_path: true

  # Location Service Routes
  - name: location-analyze
    service: location-service
    paths:
      - /api/v1/locations/analyze
    methods:
      - POST
    strip_path: true

  - name: location-score
    service: location-service
    paths:
      - /api/v1/locations/score
    methods:
      - GET
    strip_path: true

  - name: location-compare
    service: location-service
    paths:
      - /api/v1/locations/compare
    methods:
      - POST
    strip_path: true

  # Restaurant Service Routes
  - name: restaurants-crud
    service: restaurant-service
    paths:
      - /api/v1/restaurants
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    strip_path: true

  - name: restaurants-nearby
    service: restaurant-service
    paths:
      - /api/v1/restaurants/nearby
    methods:
      - GET
    strip_path: true

  - name: restaurants-search
    service: restaurant-service
    paths:
      - /api/v1/search/restaurants
    methods:
      - GET
    strip_path: true

  # Reports Service Routes
  - name: reports-templates
    service: reports-service
    paths:
      - /api/v1/reports/templates
    methods:
      - GET
    strip_path: true

  - name: reports-crud
    service: reports-service
    paths:
      - /api/v1/reports
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    strip_path: true

  - name: reports-generate
    service: reports-service
    paths:
      - /api/v1/reports/generate
      - /api/v1/reports/scheduled
      - /api/v1/reports/export
    methods:
      - GET
      - POST
    strip_path: true

  # Authentication Service Routes
  - name: auth-endpoints
    service: auth-service
    paths:
      - /api/v1/auth
    methods:
      - GET
      - POST
    strip_path: true

# Global Plugins
plugins:
  # CORS Plugin
  - name: cors
    config:
      origins:
        - "http://localhost:3000"
        - "https://*.bitebase.app"
        - "https://bitebase.app"
      methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      headers:
        - Accept
        - Accept-Version
        - Authorization
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - X-Tenant-ID
      exposed_headers:
        - X-Auth-Token
      credentials: true
      max_age: 3600

  # Request/Response Logging
  - name: file-log
    config:
      path: /var/log/kong/access.log
      reopen: true

  # Prometheus Metrics
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true

  # Request Size Limiting
  - name: request-size-limiting
    config:
      allowed_payload_size: 10  # 10MB for file uploads

  # Response Rate Limiting (Global)
  - name: response-ratelimiting
    config:
      limits:
        video: 10
        image: 100
        json: 1000

# Service-Specific Plugins
consumers:
  - username: bitebase-frontend
    keyauth_credentials:
      - key: frontend-api-key-secure-2024

  - username: bitebase-mobile
    keyauth_credentials:
      - key: mobile-api-key-secure-2024

# Service-Level Plugin Configurations
services:
  # AI Service - Higher rate limits for AI operations
  - name: ai-service
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          day: 10000
          policy: local
          fault_tolerant: true
          hide_client_headers: false

      - name: request-timeout
        config:
          http_timeout: 60000  # 60 seconds for AI processing

      - name: circuit-breaker
        config:
          max_failures: 5
          timeout: 30
          recovery_timeout: 30

  # Analytics Service - Extended timeouts for complex queries
  - name: analytics-service
    plugins:
      - name: rate-limiting
        config:
          minute: 200
          hour: 5000
          day: 50000
          policy: local

      - name: request-timeout
        config:
          http_timeout: 90000  # 90 seconds for analytics queries

      - name: response-transformer
        config:
          add:
            headers:
              - "X-Service:analytics"

  # Location Service - Optimized for geospatial queries
  - name: location-service
    plugins:
      - name: rate-limiting
        config:
          minute: 500
          hour: 10000
          day: 100000
          policy: redis
          redis_host: redis
          redis_port: 6379

      - name: request-timeout
        config:
          http_timeout: 30000

  # Restaurant Service - High throughput for core operations
  - name: restaurant-service
    plugins:
      - name: rate-limiting
        config:
          minute: 1000
          hour: 20000
          day: 200000
          policy: redis
          redis_host: redis
          redis_port: 6379

      - name: response-transformer
        config:
          add:
            headers:
              - "X-Service:restaurant"

  # Reports Service - Extended timeouts for report generation
  - name: reports-service
    plugins:
      - name: rate-limiting
        config:
          minute: 50
          hour: 1000
          day: 10000
          policy: local

      - name: request-timeout
        config:
          http_timeout: 300000  # 5 minutes for large reports

      - name: request-size-limiting
        config:
          allowed_payload_size: 50  # 50MB for report data

  # Auth Service - Strict rate limiting for security
  - name: auth-service
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
          day: 10000
          policy: redis
          redis_host: redis
          redis_port: 6379

      - name: request-timeout
        config:
          http_timeout: 15000

      - name: ip-restriction
        config:
          deny: []  # Configure based on security requirements

# Route-Level Security
routes:
  # Secure AI endpoints with JWT
  - name: ai-chat
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          key_claim_name: sub
          anonymous: false

  # Analytics requires authentication
  - name: analytics-dashboard
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          key_claim_name: sub
          anonymous: false

  # Public restaurant search with API key
  - name: restaurants-search
    plugins:
      - name: key-auth
        config:
          key_names:
            - apikey
          anonymous: false

# Health Check Routes (No authentication required)
routes:
  - name: health-checks
    paths:
      - /health
      - /ready
      - /metrics
    methods:
      - GET
    plugins:
      - name: request-transformer
        config:
          add:
            headers:
              - "X-Health-Check:true"