import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

from app.services.ai_service import AIService
from app.services.location_service import LocationService
from app.services.report_service import ReportService
from app.services.analytics_service import AnalyticsService


class TestAIService:
    """Test suite for AI Service"""

    @pytest.fixture
    def ai_service(self):
        return AIService()

    @patch('openai.ChatCompletion.create')
    def test_generate_completion_openai(self, mock_openai, ai_service):
        """Test OpenAI completion generation"""
        mock_openai.return_value = {
            "choices": [{
                "message": {
                    "content": "Test response from OpenAI"
                }
            }],
            "usage": {"total_tokens": 100}
        }
        
        result = ai_service.generate_completion(
            message="Test message",
            model="gpt-4",
            context={"tenant_id": "test-tenant"}
        )
        
        assert result["response"] == "Test response from OpenAI"
        assert result["model"] == "gpt-4"
        assert result["usage"]["tokens"] == 100

    @patch('anthropic.Anthropic.messages.create')
    def test_generate_completion_anthropic(self, mock_anthropic, ai_service):
        """Test Anthropic completion generation"""
        mock_anthropic.return_value = Mock(
            content=[Mock(text="Test response from Claude")],
            usage=Mock(input_tokens=50, output_tokens=50)
        )
        
        result = ai_service.generate_completion(
            message="Test message",
            model="claude-3-sonnet",
            context={"tenant_id": "test-tenant"}
        )
        
        assert result["response"] == "Test response from Claude"
        assert result["model"] == "claude-3-sonnet"

    @pytest.mark.asyncio
    async def test_stream_completion(self, ai_service):
        """Test streaming completion"""
        async def mock_stream():
            yield {"delta": "Hello", "type": "text"}
            yield {"delta": " world", "type": "text"}
            yield {"type": "done"}
        
        with patch.object(ai_service, '_stream_openai', return_value=mock_stream()):
            stream = ai_service.stream_completion(
                message="Test message",
                model="gpt-4"
            )
            
            chunks = []
            async for chunk in stream:
                chunks.append(chunk)
            
            assert len(chunks) == 3
            assert chunks[0]["delta"] == "Hello"
            assert chunks[-1]["type"] == "done"

    def test_error_handling(self, ai_service):
        """Test error handling in AI service"""
        with patch('openai.ChatCompletion.create', side_effect=Exception("API Error")):
            with pytest.raises(Exception) as exc_info:
                ai_service.generate_completion("Test message")
            
            assert "API Error" in str(exc_info.value)


class TestLocationService:
    """Test suite for Location Service"""

    @pytest.fixture
    def location_service(self):
        return LocationService()

    @patch('googlemaps.Client.geocode')
    @patch('googlemaps.Client.places_nearby')
    def test_analyze_location(self, mock_places, mock_geocode, location_service):
        """Test location analysis"""
        # Mock geocoding response
        mock_geocode.return_value = [{
            'geometry': {
                'location': {'lat': 40.7128, 'lng': -74.0060}
            },
            'formatted_address': '123 Main St, New York, NY'
        }]
        
        # Mock nearby places response
        mock_places.return_value = {
            'results': [
                {'name': 'Restaurant A', 'rating': 4.5, 'types': ['restaurant']},
                {'name': 'Restaurant B', 'rating': 4.0, 'types': ['restaurant']},
            ]
        }
        
        result = location_service.analyze_location(
            address="123 Main St, New York, NY",
            radius=1.0
        )
        
        assert result["coordinates"]["lat"] == 40.7128
        assert result["coordinates"]["lng"] == -74.0060
        assert len(result["nearby_competitors"]) == 2
        assert result["competition_score"] > 0

    def test_demographic_analysis(self, location_service):
        """Test demographic analysis"""
        coordinates = {"lat": 40.7128, "lng": -74.0060}
        
        with patch.object(location_service, '_fetch_demographic_data') as mock_demo:
            mock_demo.return_value = {
                "population": 50000,
                "median_income": 65000,
                "age_distribution": {"18-34": 35, "35-54": 40, "55+": 25}
            }
            
            result = location_service.get_demographic_analysis(coordinates)
            
            assert result["population"] == 50000
            assert result["median_income"] == 65000
            assert "age_distribution" in result

    def test_location_scoring(self, location_service):
        """Test location scoring algorithm"""
        location_data = {
            "demographics": {"population": 50000, "median_income": 65000},
            "competition": {"nearby_restaurants": 10, "avg_rating": 4.0},
            "accessibility": {"foot_traffic": "high", "parking": "available"}
        }
        
        score = location_service.calculate_location_score(location_data)
        
        assert 0 <= score <= 100
        assert isinstance(score, (int, float))


class TestReportService:
    """Test suite for Report Service"""

    @pytest.fixture
    def report_service(self):
        return ReportService()

    def test_generate_market_analysis_report(self, report_service):
        """Test market analysis report generation"""
        parameters = {
            "time_period": "last_quarter",
            "include_forecasts": True,
            "tenant_id": "test-tenant"
        }
        
        with patch.object(report_service, '_fetch_market_data') as mock_data:
            mock_data.return_value = {
                "revenue": 100000,
                "growth_rate": 0.15,
                "customer_count": 1500
            }
            
            report = report_service.generate_report(
                report_type="market_analysis",
                parameters=parameters
            )
            
            assert report["type"] == "market_analysis"
            assert "executive_summary" in report["data"]
            assert "key_findings" in report["data"]
            assert "recommendations" in report["data"]

    def test_generate_competitive_analysis_report(self, report_service):
        """Test competitive analysis report generation"""
        parameters = {
            "location": "New York, NY",
            "radius": 2.0,
            "tenant_id": "test-tenant"
        }
        
        report = report_service.generate_report(
            report_type="competitive_analysis",
            parameters=parameters
        )
        
        assert report["type"] == "competitive_analysis"
        assert "competitor_analysis" in report["data"]
        assert "market_positioning" in report["data"]

    def test_report_caching(self, report_service):
        """Test report caching mechanism"""
        parameters = {"tenant_id": "test-tenant", "time_period": "last_month"}
        
        # First call should generate report
        report1 = report_service.generate_report("market_analysis", parameters)
        
        # Second call should return cached report
        with patch.object(report_service, '_generate_market_analysis') as mock_gen:
            report2 = report_service.generate_report("market_analysis", parameters)
            
            # Should not call generation method again
            mock_gen.assert_not_called()
            assert report1["report_id"] == report2["report_id"]


class TestAnalyticsService:
    """Test suite for Analytics Service"""

    @pytest.fixture
    def analytics_service(self):
        return AnalyticsService()

    def test_track_copilotkit_usage(self, analytics_service):
        """Test CopilotKit usage tracking"""
        usage_data = {
            "user_id": "test-user",
            "tenant_id": "test-tenant",
            "action": "chat_completion",
            "model": "gpt-4",
            "tokens_used": 100,
            "response_time": 1.5
        }
        
        result = analytics_service.track_copilotkit_usage(usage_data)
        
        assert result["status"] == "tracked"
        assert "event_id" in result

    def test_get_performance_metrics(self, analytics_service):
        """Test performance metrics retrieval"""
        metrics = analytics_service.get_performance_metrics(
            tenant_id="test-tenant",
            period="7d"
        )
        
        assert "response_times" in metrics
        assert "usage_stats" in metrics
        assert "error_rates" in metrics
        assert "system_health" in metrics

    def test_get_usage_analytics(self, analytics_service):
        """Test usage analytics"""
        analytics = analytics_service.get_usage_analytics(
            tenant_id="test-tenant",
            period="30d"
        )
        
        assert "total_queries" in analytics
        assert "unique_users" in analytics
        assert "popular_actions" in analytics
        assert "usage_trends" in analytics

    def test_real_time_metrics(self, analytics_service):
        """Test real-time metrics calculation"""
        metrics = analytics_service.get_real_time_metrics("test-tenant")
        
        assert "active_users" in metrics
        assert "current_load" in metrics
        assert "response_time" in metrics
        assert "error_rate" in metrics


if __name__ == "__main__":
    pytest.main([__file__])
