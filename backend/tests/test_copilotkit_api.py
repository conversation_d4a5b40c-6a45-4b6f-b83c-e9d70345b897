import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient
from fastapi.websockets import WebSocket
from unittest.mock import Mock, patch, AsyncMock
import json
from datetime import datetime, timedelta

from app.main import app
from app.api.v1.copilotkit import copilotkit_router
from app.schemas.copilotkit import (
    ChatCompletionRequest,
    StreamingRequest,
    LocationAnalysisRequest,
    ReportGenerationRequest,
    PerformanceMetrics
)
from app.auth.auth_manager import get_current_user


# Test client
client = TestClient(app)

# Mock user for testing
mock_user = {
    "id": "test-user-1",
    "email": "<EMAIL>",
    "name": "Test User",
    "role": "admin",
    "tenant_id": "test-tenant-1",
    "permissions": ["read", "write", "admin"]
}


class TestCopilotKitAPI:
    """Test suite for CopilotKit API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.mock_user = mock_user
        
    def test_health_endpoint(self):
        """Test health check endpoint"""
        response = client.get("/api/v1/copilotkit/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data

    @patch('app.api.v1.copilotkit.get_current_user')
    @patch('app.services.ai_service.AIService.generate_completion')
    def test_chat_completion(self, mock_ai_service, mock_auth):
        """Test chat completion endpoint"""
        mock_auth.return_value = self.mock_user
        mock_ai_service.return_value = {
            "response": "This is a test response",
            "model": "gpt-4",
            "usage": {"tokens": 100}
        }
        
        request_data = {
            "message": "What's my revenue for last month?",
            "context": {"tenant_id": "test-tenant-1"},
            "model": "gpt-4",
            "temperature": 0.7
        }
        
        response = client.post(
            "/api/v1/copilotkit/chat/completion",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["response"] == "This is a test response"
        assert data["model"] == "gpt-4"

    @patch('app.api.v1.copilotkit.get_current_user')
    @patch('app.services.ai_service.AIService.stream_completion')
    def test_streaming_endpoint(self, mock_ai_service, mock_auth):
        """Test streaming completion endpoint"""
        mock_auth.return_value = self.mock_user
        
        # Mock streaming response
        async def mock_stream():
            yield {"delta": "Hello", "type": "text"}
            yield {"delta": " world", "type": "text"}
            yield {"delta": "!", "type": "text"}
            yield {"type": "done"}
        
        mock_ai_service.return_value = mock_stream()
        
        request_data = {
            "message": "Tell me about my business",
            "context": {"tenant_id": "test-tenant-1"},
            "stream": True
        }
        
        response = client.post(
            "/api/v1/copilotkit/chat/stream",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream"

    @patch('app.api.v1.copilotkit.get_current_user')
    @patch('app.services.location_service.LocationService.analyze_location')
    def test_location_analysis(self, mock_location_service, mock_auth):
        """Test location analysis endpoint"""
        mock_auth.return_value = self.mock_user
        mock_location_service.return_value = {
            "location_id": "loc-123",
            "score": 85.5,
            "demographics": {
                "population": 50000,
                "median_income": 65000,
                "age_groups": {"18-34": 35, "35-54": 40, "55+": 25}
            },
            "competition": {
                "nearby_restaurants": 15,
                "direct_competitors": 3,
                "market_saturation": "medium"
            },
            "recommendations": [
                "Consider lunch specials for office workers",
                "Target family dining for evening hours"
            ]
        }
        
        request_data = {
            "address": "123 Main St, Anytown, USA",
            "radius": 1.0,
            "analysis_type": "comprehensive"
        }
        
        response = client.post(
            "/api/v1/copilotkit/location/analyze",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["score"] == 85.5
        assert "demographics" in data
        assert "competition" in data

    @patch('app.api.v1.copilotkit.get_current_user')
    @patch('app.services.report_service.ReportService.generate_report')
    def test_report_generation(self, mock_report_service, mock_auth):
        """Test report generation endpoint"""
        mock_auth.return_value = self.mock_user
        mock_report_service.return_value = {
            "report_id": "report-123",
            "type": "market_analysis",
            "status": "completed",
            "data": {
                "executive_summary": "Market analysis shows strong growth potential",
                "key_findings": [
                    "Revenue increased 15% year-over-year",
                    "Customer satisfaction improved to 4.2/5"
                ],
                "recommendations": [
                    "Expand delivery services",
                    "Introduce loyalty program"
                ]
            },
            "generated_at": datetime.now().isoformat()
        }
        
        request_data = {
            "report_type": "market_analysis",
            "parameters": {
                "time_period": "last_quarter",
                "include_forecasts": True
            }
        }
        
        response = client.post(
            "/api/v1/copilotkit/reports/generate",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["type"] == "market_analysis"
        assert data["status"] == "completed"
        assert "executive_summary" in data["data"]

    @patch('app.api.v1.copilotkit.get_current_user')
    def test_metrics_endpoint(self, mock_auth):
        """Test metrics endpoint"""
        mock_auth.return_value = self.mock_user
        
        response = client.get(
            "/api/v1/copilotkit/metrics",
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "performance" in data
        assert "usage" in data
        assert "system_health" in data

    @patch('app.api.v1.copilotkit.get_current_user')
    def test_analytics_endpoint(self, mock_auth):
        """Test analytics endpoint"""
        mock_auth.return_value = self.mock_user
        
        response = client.get(
            "/api/v1/copilotkit/analytics",
            params={"period": "7d"},
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "usage_stats" in data
        assert "performance_trends" in data

    def test_unauthorized_access(self):
        """Test that endpoints require authentication"""
        response = client.post("/api/v1/copilotkit/chat/completion", json={})
        assert response.status_code == 401

    @patch('app.api.v1.copilotkit.get_current_user')
    def test_invalid_request_data(self, mock_auth):
        """Test handling of invalid request data"""
        mock_auth.return_value = self.mock_user
        
        # Missing required fields
        response = client.post(
            "/api/v1/copilotkit/chat/completion",
            json={},
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code == 422  # Validation error

    @patch('app.api.v1.copilotkit.get_current_user')
    @patch('app.services.ai_service.AIService.generate_completion')
    def test_service_error_handling(self, mock_ai_service, mock_auth):
        """Test handling of service errors"""
        mock_auth.return_value = self.mock_user
        mock_ai_service.side_effect = Exception("AI service unavailable")
        
        request_data = {
            "message": "Test message",
            "context": {"tenant_id": "test-tenant-1"}
        }
        
        response = client.post(
            "/api/v1/copilotkit/chat/completion",
            json=request_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code == 500
        data = response.json()
        assert "error" in data


class TestWebSocketConnection:
    """Test WebSocket functionality"""

    @pytest.mark.asyncio
    async def test_websocket_connection(self):
        """Test WebSocket connection establishment"""
        with client.websocket_connect("/api/v1/copilotkit/ws/test-user-1") as websocket:
            # Test connection
            data = websocket.receive_json()
            assert data["type"] == "connection_established"

    @pytest.mark.asyncio
    async def test_websocket_message_handling(self):
        """Test WebSocket message handling"""
        with client.websocket_connect("/api/v1/copilotkit/ws/test-user-1") as websocket:
            # Send test message
            test_message = {
                "type": "chat_message",
                "content": "Hello AI",
                "context": {"tenant_id": "test-tenant-1"}
            }
            websocket.send_json(test_message)
            
            # Receive response
            response = websocket.receive_json()
            assert response["type"] in ["ai_response", "error"]

    @pytest.mark.asyncio
    async def test_websocket_error_handling(self):
        """Test WebSocket error handling"""
        with client.websocket_connect("/api/v1/copilotkit/ws/test-user-1") as websocket:
            # Send invalid message
            websocket.send_text("invalid json")
            
            # Should receive error response
            response = websocket.receive_json()
            assert response["type"] == "error"


class TestSecurityMiddleware:
    """Test security middleware functionality"""

    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        # Make multiple requests quickly
        for i in range(10):
            response = client.get("/api/v1/copilotkit/health")
            if i < 5:
                assert response.status_code == 200
            else:
                # Should be rate limited after 5 requests
                assert response.status_code in [200, 429]

    @patch('app.api.v1.copilotkit.get_current_user')
    def test_permission_checking(self, mock_auth):
        """Test permission-based access control"""
        # User with limited permissions
        limited_user = {
            **mock_user,
            "permissions": ["read"]  # No write permissions
        }
        mock_auth.return_value = limited_user
        
        # Try to access write-required endpoint
        response = client.post(
            "/api/v1/copilotkit/reports/generate",
            json={"report_type": "test"},
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Should be forbidden due to insufficient permissions
        assert response.status_code == 403


if __name__ == "__main__":
    pytest.main([__file__])
