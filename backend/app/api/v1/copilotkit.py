"""
Enhanced CopilotKit API endpoints for BiteBase Intelligence
Provides AI-powered assistant capabilities with streaming and real-time features
"""

from fastapi import APIRouter, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, Optional, AsyncGenerator
import json
import asyncio
import logging
import time
from datetime import datetime

from app.auth.auth_manager import get_current_user, get_current_tenant, require_permission
from app.core.database import get_db
from app.models.user import User
from app.schemas.copilotkit import (
    CopilotRequest,
    CopilotResponse,
    StreamingRequest,
    ActionRequest,
    ActionResponse,
    ConversationHistory,
    AnalysisRequest,
    ReportRequest
)
from app.services.ai_service import AIService
from app.services.location_service import LocationService
from app.services.analytics_service import AnalyticsService
from app.services.report_service import ReportService
from app.monitoring.copilotkit_monitor import copilotkit_monitor

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/copilotkit", tags=["CopilotKit"])

# Initialize services
ai_service = AIService()
location_service = LocationService()
analytics_service = AnalyticsService()
report_service = ReportService()

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.user_connections[user_id] = websocket

    def disconnect(self, websocket: WebSocket, user_id: str):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if user_id in self.user_connections:
            del self.user_connections[user_id]

    async def send_personal_message(self, message: str, user_id: str):
        if user_id in self.user_connections:
            await self.user_connections[user_id].send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()


@router.post("/chat", response_model=CopilotResponse)
async def chat_completion(
    request: CopilotRequest,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Enhanced chat completion with context awareness and tool integration
    """
    try:
        # Validate user permissions
        if not current_user.is_active:
            raise HTTPException(status_code=403, detail="User account is inactive")

        # Process the chat request with enhanced context
        response = await ai_service.process_chat_request(
            message=request.message,
            context=request.context,
            conversation_history=request.conversation_history,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            tools=request.tools,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )

        return CopilotResponse(
            message=response.message,
            context=response.context,
            tools_used=response.tools_used,
            confidence=response.confidence,
            sources=response.sources,
            suggestions=response.suggestions,
            metadata={
                "response_time": response.response_time,
                "tokens_used": response.tokens_used,
                "model": request.model,
                "timestamp": datetime.utcnow().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"Chat completion error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chat completion failed: {str(e)}")


@router.post("/stream")
async def stream_chat(
    request: StreamingRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Streaming chat completion for real-time responses
    """
    async def generate_stream() -> AsyncGenerator[str, None]:
        try:
            async for chunk in ai_service.stream_chat_response(
                message=request.message,
                context=request.context,
                user_id=current_user.id,
                tenant_id=current_user.tenant_id,
                model=request.model
            ):
                yield f"data: {json.dumps(chunk)}\n\n"
        except Exception as e:
            error_chunk = {
                "error": True,
                "message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
            yield f"data: {json.dumps(error_chunk)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*"
        }
    )


@router.post("/actions/analyze-location", response_model=ActionResponse)
async def analyze_location(
    request: AnalysisRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Enhanced location analysis with AI insights
    """
    try:
        # Validate permissions
        if not current_user.has_permission("locations:analyze"):
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        # Perform location analysis
        analysis = await location_service.analyze_location(
            latitude=request.latitude,
            longitude=request.longitude,
            radius_km=request.radius,
            analysis_type=request.analysis_type,
            tenant_id=current_user.tenant_id
        )

        # Generate AI insights
        insights = await ai_service.generate_location_insights(
            analysis_data=analysis,
            context=request.context,
            user_preferences=current_user.preferences
        )

        return ActionResponse(
            success=True,
            action="analyze_location",
            data={
                "location": {"latitude": request.latitude, "longitude": request.longitude},
                "analysis": analysis,
                "insights": insights.insights,
                "recommendations": insights.recommendations,
                "confidence": insights.confidence
            },
            metadata={
                "analysis_type": request.analysis_type,
                "radius_km": request.radius,
                "timestamp": datetime.utcnow().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"Location analysis error: {str(e)}")
        return ActionResponse(
            success=False,
            action="analyze_location",
            error=str(e),
            metadata={"timestamp": datetime.utcnow().isoformat()}
        )


@router.post("/actions/generate-report", response_model=ActionResponse)
async def generate_market_report(
    request: ReportRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Generate comprehensive market analysis reports
    """
    try:
        # Validate permissions
        if not current_user.has_permission("reports:generate"):
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        # Generate report
        report = await report_service.generate_market_report(
            location=request.location,
            report_type=request.report_type,
            timeframe=request.timeframe,
            tenant_id=current_user.tenant_id,
            user_id=current_user.id
        )

        # Generate AI summary and insights
        ai_summary = await ai_service.generate_report_summary(
            report_data=report,
            context=request.context,
            user_preferences=current_user.preferences
        )

        return ActionResponse(
            success=True,
            action="generate_report",
            data={
                "report_id": report.id,
                "report_type": request.report_type,
                "location": request.location,
                "executive_summary": ai_summary.executive_summary,
                "key_findings": ai_summary.key_findings,
                "recommendations": ai_summary.recommendations,
                "download_url": report.download_url
            },
            metadata={
                "timeframe": request.timeframe,
                "generated_at": datetime.utcnow().isoformat(),
                "expires_at": report.expires_at.isoformat() if report.expires_at else None
            }
        )

    except Exception as e:
        logger.error(f"Report generation error: {str(e)}")
        return ActionResponse(
            success=False,
            action="generate_report",
            error=str(e),
            metadata={"timestamp": datetime.utcnow().isoformat()}
        )


@router.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """
    WebSocket endpoint for real-time AI assistant communication
    """
    await manager.connect(websocket, user_id)
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Process message based on type
            if message_data.get("type") == "chat":
                # Handle real-time chat
                response = await ai_service.process_realtime_message(
                    message=message_data.get("message"),
                    user_id=user_id,
                    context=message_data.get("context", {})
                )
                await manager.send_personal_message(
                    json.dumps(response), 
                    user_id
                )
            
            elif message_data.get("type") == "action":
                # Handle action requests
                action_response = await ai_service.process_action_request(
                    action=message_data.get("action"),
                    parameters=message_data.get("parameters", {}),
                    user_id=user_id
                )
                await manager.send_personal_message(
                    json.dumps(action_response), 
                    user_id
                )

    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
        logger.info(f"WebSocket disconnected for user: {user_id}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {str(e)}")
        manager.disconnect(websocket, user_id)


@router.get("/conversation/{conversation_id}", response_model=ConversationHistory)
async def get_conversation_history(
    conversation_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Retrieve conversation history for a specific conversation
    """
    try:
        history = await ai_service.get_conversation_history(
            conversation_id=conversation_id,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id
        )
        
        return ConversationHistory(
            conversation_id=conversation_id,
            messages=history.messages,
            metadata=history.metadata,
            created_at=history.created_at,
            updated_at=history.updated_at
        )

    except Exception as e:
        logger.error(f"Error retrieving conversation history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve conversation history")


@router.get("/health")
async def copilotkit_health():
    """
    Health check for CopilotKit services
    """
    try:
        system_health = await copilotkit_monitor.get_system_health()

        return {
            "status": "healthy",
            "service": "copilotkit-api",
            "version": "2.0.0",
            "features": {
                "streaming": True,
                "websockets": True,
                "location_analysis": True,
                "report_generation": True,
                "real_time_chat": True
            },
            "system_health": {
                "cpu_usage": system_health.cpu_usage,
                "memory_usage": system_health.memory_usage,
                "active_connections": system_health.active_connections,
                "response_time_avg": system_health.response_time_avg
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "degraded",
            "service": "copilotkit-api",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/metrics")
async def get_metrics(
    current_user: User = Depends(get_current_user)
):
    """
    Get performance metrics for current tenant
    """
    try:
        dashboard = await copilotkit_monitor.get_performance_dashboard(
            tenant_id=current_user.tenant_id
        )

        return {
            "status": "success",
            "data": dashboard
        }
    except Exception as e:
        logger.error(f"Error getting metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve metrics")


@router.get("/analytics")
async def get_analytics(
    timeframe: str = "24h",
    current_user: User = Depends(get_current_user)
):
    """
    Get usage analytics for CopilotKit features
    """
    try:
        analytics = await analytics_service.get_usage_analytics(
            tenant_id=current_user.tenant_id,
            timeframe=timeframe
        )

        return {
            "status": "success",
            "data": analytics,
            "timeframe": timeframe
        }
    except Exception as e:
        logger.error(f"Error getting analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve analytics")
