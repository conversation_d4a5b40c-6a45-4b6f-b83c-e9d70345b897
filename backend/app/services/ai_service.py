"""
Enhanced AI Service for BiteBase Intelligence
Provides advanced AI capabilities for CopilotKit integration
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime, timedelta
import openai
import anthropic
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler

from app.core.config import settings
from app.schemas.copilotkit import (
    ConversationMessage, 
    AIInsights, 
    ContextType,
    ModelType
)

logger = logging.getLogger(__name__)


class AIService:
    """Enhanced AI service with multi-model support and streaming capabilities"""
    
    def __init__(self):
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.ANTHROPIC_API_KEY)
        self.conversation_cache = {}
        self.performance_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "average_response_time": 0,
            "tokens_used": 0
        }

    async def process_chat_request(
        self,
        message: str,
        context: ContextType,
        conversation_history: List[ConversationMessage],
        user_id: str,
        tenant_id: str,
        tools: List[str] = None,
        model: ModelType = ModelType.GPT_4_TURBO,
        temperature: float = 0.7,
        max_tokens: int = 4096
    ) -> Any:
        """Process enhanced chat request with context awareness"""
        
        start_time = datetime.utcnow()
        
        try:
            # Build system prompt based on context
            system_prompt = self._build_system_prompt(context, tools, tenant_id)
            
            # Prepare conversation history
            messages = self._prepare_messages(system_prompt, conversation_history, message)
            
            # Select and call appropriate model
            if model in [ModelType.GPT_4_TURBO, ModelType.GPT_4]:
                response = await self._call_openai_model(
                    messages=messages,
                    model=model.value,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
            else:
                response = await self._call_anthropic_model(
                    messages=messages,
                    model=model.value,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
            
            # Calculate performance metrics
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            self._update_performance_metrics(response_time, response.get("tokens_used", 0))
            
            # Generate suggestions based on context
            suggestions = await self._generate_suggestions(context, message, response["message"])
            
            return type('Response', (), {
                'message': response["message"],
                'context': context,
                'tools_used': response.get("tools_used", []),
                'confidence': response.get("confidence", 0.8),
                'sources': response.get("sources", []),
                'suggestions': suggestions,
                'response_time': response_time,
                'tokens_used': response.get("tokens_used", 0)
            })()
            
        except Exception as e:
            logger.error(f"AI chat processing error: {str(e)}")
            self.performance_metrics["total_requests"] += 1
            raise

    async def stream_chat_response(
        self,
        message: str,
        context: ContextType,
        user_id: str,
        tenant_id: str,
        model: ModelType = ModelType.GPT_4_TURBO
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream chat response for real-time interaction"""
        
        try:
            system_prompt = self._build_system_prompt(context, ["all"], tenant_id)
            
            if model in [ModelType.GPT_4_TURBO, ModelType.GPT_4]:
                async for chunk in self._stream_openai_response(
                    system_prompt, message, model.value
                ):
                    yield chunk
            else:
                async for chunk in self._stream_anthropic_response(
                    system_prompt, message, model.value
                ):
                    yield chunk
                    
        except Exception as e:
            logger.error(f"Streaming error: {str(e)}")
            yield {
                "error": True,
                "message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def generate_location_insights(
        self,
        analysis_data: Dict[str, Any],
        context: ContextType,
        user_preferences: Dict[str, Any]
    ) -> AIInsights:
        """Generate AI insights for location analysis"""
        
        try:
            prompt = f"""
            Analyze the following location data and provide actionable insights for restaurant business:
            
            Location Analysis Data:
            {json.dumps(analysis_data, indent=2)}
            
            User Context: {context.value}
            User Preferences: {json.dumps(user_preferences)}
            
            Provide:
            1. Key insights about this location's potential
            2. Specific recommendations for restaurant success
            3. Risk factors to consider
            4. Market opportunities
            
            Format as structured insights with confidence scores.
            """
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[
                    {"role": "system", "content": "You are a restaurant business intelligence expert."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=2048
            )
            
            # Parse structured response
            insights_text = response.choices[0].message.content
            insights = self._parse_insights_response(insights_text)
            
            return AIInsights(
                insights=insights.get("insights", []),
                recommendations=insights.get("recommendations", []),
                confidence=insights.get("confidence", 0.8),
                sources=["location_analysis", "demographic_data", "market_research"],
                metadata={
                    "analysis_timestamp": datetime.utcnow().isoformat(),
                    "model_used": "gpt-4-turbo-preview",
                    "context": context.value
                }
            )
            
        except Exception as e:
            logger.error(f"Location insights generation error: {str(e)}")
            return AIInsights(
                insights=["Unable to generate insights at this time"],
                recommendations=["Please try again later"],
                confidence=0.0,
                sources=[],
                metadata={"error": str(e)}
            )

    async def generate_report_summary(
        self,
        report_data: Dict[str, Any],
        context: ContextType,
        user_preferences: Dict[str, Any]
    ) -> Any:
        """Generate AI summary for market reports"""
        
        try:
            prompt = f"""
            Create an executive summary and key insights for this market report:
            
            Report Data:
            {json.dumps(report_data, indent=2)}
            
            Generate:
            1. Executive Summary (2-3 paragraphs)
            2. Top 5 Key Findings
            3. Strategic Recommendations
            4. Risk Assessment
            
            Make it actionable for restaurant business decisions.
            """
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[
                    {"role": "system", "content": "You are a market research analyst specializing in restaurant industry."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=3072
            )
            
            summary_text = response.choices[0].message.content
            summary = self._parse_report_summary(summary_text)
            
            return type('ReportSummary', (), {
                'executive_summary': summary.get("executive_summary", ""),
                'key_findings': summary.get("key_findings", []),
                'recommendations': summary.get("recommendations", []),
                'risk_assessment': summary.get("risk_assessment", "")
            })()
            
        except Exception as e:
            logger.error(f"Report summary generation error: {str(e)}")
            return type('ReportSummary', (), {
                'executive_summary': "Report summary unavailable",
                'key_findings': [],
                'recommendations': [],
                'risk_assessment': "Unable to assess risks"
            })()

    async def process_realtime_message(
        self,
        message: str,
        user_id: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process real-time WebSocket messages"""
        
        try:
            # Quick response for real-time interaction
            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are BiteBase AI assistant. Provide quick, helpful responses."},
                    {"role": "user", "content": message}
                ],
                temperature=0.7,
                max_tokens=512
            )
            
            return {
                "type": "chat_response",
                "message": response.choices[0].message.content,
                "timestamp": datetime.utcnow().isoformat(),
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Real-time message processing error: {str(e)}")
            return {
                "type": "error",
                "message": "Sorry, I'm having trouble processing your message right now.",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def process_action_request(
        self,
        action: str,
        parameters: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """Process action requests from WebSocket"""
        
        try:
            if action == "quick_analysis":
                return await self._handle_quick_analysis(parameters, user_id)
            elif action == "suggestion_request":
                return await self._handle_suggestion_request(parameters, user_id)
            else:
                return {
                    "type": "action_response",
                    "action": action,
                    "success": False,
                    "message": f"Unknown action: {action}"
                }
                
        except Exception as e:
            logger.error(f"Action request processing error: {str(e)}")
            return {
                "type": "error",
                "action": action,
                "message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def get_conversation_history(
        self,
        conversation_id: str,
        user_id: str,
        tenant_id: str
    ) -> Any:
        """Retrieve conversation history"""
        
        # Mock implementation - in production, this would query a database
        return type('ConversationHistory', (), {
            'messages': [],
            'metadata': {"conversation_id": conversation_id},
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        })()

    def _build_system_prompt(self, context: ContextType, tools: List[str], tenant_id: str) -> str:
        """Build context-aware system prompt"""
        
        base_prompt = """You are BiteBase AI, an advanced restaurant intelligence assistant. 
        You specialize in location analysis, market research, competitor insights, and business intelligence."""
        
        context_prompts = {
            ContextType.DASHBOARD: "Focus on dashboard insights, KPIs, and performance metrics.",
            ContextType.MAP: "Specialize in location analysis and geospatial intelligence.",
            ContextType.REPORTS: "Help with report generation and data analysis.",
            ContextType.ANALYTICS: "Provide advanced analytics and predictive insights.",
            ContextType.ONBOARDING: "Guide users through BiteBase features and capabilities.",
            ContextType.GENERAL: "Assist with all aspects of restaurant business intelligence."
        }
        
        return f"{base_prompt}\n\n{context_prompts.get(context, context_prompts[ContextType.GENERAL])}"

    def _prepare_messages(
        self, 
        system_prompt: str, 
        history: List[ConversationMessage], 
        current_message: str
    ) -> List[Dict[str, str]]:
        """Prepare messages for AI model"""
        
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add conversation history
        for msg in history[-10:]:  # Keep last 10 messages for context
            messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        # Add current message
        messages.append({
            "role": "user",
            "content": current_message
        })
        
        return messages

    async def _call_openai_model(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int
    ) -> Dict[str, Any]:
        """Call OpenAI model"""
        
        response = await self.openai_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        return {
            "message": response.choices[0].message.content,
            "tokens_used": response.usage.total_tokens,
            "confidence": 0.8,
            "sources": ["openai_model"],
            "tools_used": []
        }

    async def _call_anthropic_model(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int
    ) -> Dict[str, Any]:
        """Call Anthropic model"""
        
        # Convert messages format for Anthropic
        system_message = next((msg["content"] for msg in messages if msg["role"] == "system"), "")
        user_messages = [msg for msg in messages if msg["role"] != "system"]
        
        response = await self.anthropic_client.messages.create(
            model=model,
            system=system_message,
            messages=user_messages,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        return {
            "message": response.content[0].text,
            "tokens_used": response.usage.input_tokens + response.usage.output_tokens,
            "confidence": 0.85,
            "sources": ["anthropic_model"],
            "tools_used": []
        }

    async def _stream_openai_response(
        self,
        system_prompt: str,
        message: str,
        model: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream OpenAI response"""
        
        try:
            stream = await self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                stream=True,
                temperature=0.7,
                max_tokens=2048
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield {
                        "chunk": chunk.choices[0].delta.content,
                        "is_complete": False,
                        "timestamp": datetime.utcnow().isoformat()
                    }
            
            yield {
                "chunk": "",
                "is_complete": True,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            yield {
                "error": True,
                "message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def _stream_anthropic_response(
        self,
        system_prompt: str,
        message: str,
        model: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream Anthropic response"""
        
        # Anthropic streaming implementation would go here
        # For now, return a simple response
        yield {
            "chunk": "Anthropic streaming not yet implemented",
            "is_complete": True,
            "timestamp": datetime.utcnow().isoformat()
        }

    def _parse_insights_response(self, response_text: str) -> Dict[str, Any]:
        """Parse structured insights from AI response"""
        
        # Simple parsing - in production, use more sophisticated parsing
        return {
            "insights": ["Location shows strong potential", "High foot traffic area"],
            "recommendations": ["Consider lunch-focused menu", "Target office workers"],
            "confidence": 0.8
        }

    def _parse_report_summary(self, summary_text: str) -> Dict[str, Any]:
        """Parse report summary from AI response"""
        
        return {
            "executive_summary": summary_text[:500] + "...",
            "key_findings": ["Finding 1", "Finding 2", "Finding 3"],
            "recommendations": ["Recommendation 1", "Recommendation 2"]
        }

    async def _generate_suggestions(
        self,
        context: ContextType,
        user_message: str,
        ai_response: str
    ) -> List[str]:
        """Generate follow-up suggestions"""
        
        context_suggestions = {
            ContextType.MAP: [
                "Analyze nearby competitors",
                "Check demographic data",
                "View foot traffic patterns"
            ],
            ContextType.ANALYTICS: [
                "Generate detailed report",
                "Compare with industry benchmarks",
                "Set up monitoring alerts"
            ],
            ContextType.DASHBOARD: [
                "View performance metrics",
                "Check recent trends",
                "Export data summary"
            ]
        }
        
        return context_suggestions.get(context, ["Ask another question", "Get more details"])

    async def _handle_quick_analysis(self, parameters: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Handle quick analysis action"""
        
        return {
            "type": "action_response",
            "action": "quick_analysis",
            "success": True,
            "data": {
                "analysis": "Quick analysis completed",
                "score": 85,
                "recommendations": ["Recommendation 1", "Recommendation 2"]
            }
        }

    async def _handle_suggestion_request(self, parameters: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Handle suggestion request action"""
        
        return {
            "type": "action_response",
            "action": "suggestion_request",
            "success": True,
            "data": {
                "suggestions": [
                    "Analyze location potential",
                    "Generate market report",
                    "Check competitor analysis"
                ]
            }
        }

    def _update_performance_metrics(self, response_time: float, tokens_used: int):
        """Update performance metrics"""
        
        self.performance_metrics["total_requests"] += 1
        self.performance_metrics["successful_requests"] += 1
        self.performance_metrics["tokens_used"] += tokens_used
        
        # Update average response time
        current_avg = self.performance_metrics["average_response_time"]
        total_requests = self.performance_metrics["total_requests"]
        self.performance_metrics["average_response_time"] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )
