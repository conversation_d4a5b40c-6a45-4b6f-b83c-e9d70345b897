"""
Enhanced Analytics Service for BiteBase Intelligence
Provides real-time analytics and performance monitoring
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json
import random
from collections import defaultdict

logger = logging.getLogger(__name__)


class AnalyticsService:
    """Enhanced analytics service with real-time capabilities"""
    
    def __init__(self):
        self.metrics_cache = defaultdict(list)
        self.performance_data = {}
        self.real_time_sessions = {}
        
    async def track_copilotkit_usage(
        self,
        user_id: str,
        tenant_id: str,
        action: str,
        context: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Track CopilotKit usage analytics"""
        
        try:
            usage_event = {
                "timestamp": datetime.utcnow(),
                "user_id": user_id,
                "tenant_id": tenant_id,
                "action": action,
                "context": context,
                "metadata": metadata or {},
                "session_id": self._get_session_id(user_id)
            }
            
            # Store in metrics cache
            self.metrics_cache[f"copilotkit_usage_{tenant_id}"].append(usage_event)
            
            # Update real-time metrics
            await self._update_realtime_metrics(tenant_id, action, context)
            
            logger.info(f"Tracked CopilotKit usage: {action} for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error tracking CopilotKit usage: {str(e)}")
    
    async def get_usage_analytics(
        self,
        tenant_id: str,
        timeframe: str = "24h",
        metrics: List[str] = None
    ) -> Dict[str, Any]:
        """Get comprehensive usage analytics"""
        
        try:
            # Calculate timeframe
            end_time = datetime.utcnow()
            if timeframe == "1h":
                start_time = end_time - timedelta(hours=1)
            elif timeframe == "24h":
                start_time = end_time - timedelta(days=1)
            elif timeframe == "7d":
                start_time = end_time - timedelta(days=7)
            elif timeframe == "30d":
                start_time = end_time - timedelta(days=30)
            else:
                start_time = end_time - timedelta(days=1)
            
            # Get usage data
            usage_data = self.metrics_cache.get(f"copilotkit_usage_{tenant_id}", [])
            filtered_data = [
                event for event in usage_data
                if start_time <= event["timestamp"] <= end_time
            ]
            
            # Calculate analytics
            analytics = {
                "timeframe": timeframe,
                "total_interactions": len(filtered_data),
                "unique_users": len(set(event["user_id"] for event in filtered_data)),
                "popular_actions": self._calculate_popular_actions(filtered_data),
                "context_distribution": self._calculate_context_distribution(filtered_data),
                "hourly_usage": self._calculate_hourly_usage(filtered_data),
                "user_engagement": self._calculate_user_engagement(filtered_data),
                "performance_metrics": await self._get_performance_metrics(tenant_id, timeframe),
                "trends": self._calculate_trends(filtered_data, timeframe)
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting usage analytics: {str(e)}")
            return self._get_default_analytics()
    
    async def get_realtime_metrics(
        self,
        tenant_id: str
    ) -> Dict[str, Any]:
        """Get real-time metrics for dashboard"""
        
        try:
            current_time = datetime.utcnow()
            last_hour = current_time - timedelta(hours=1)
            
            # Get recent usage data
            usage_data = self.metrics_cache.get(f"copilotkit_usage_{tenant_id}", [])
            recent_data = [
                event for event in usage_data
                if event["timestamp"] >= last_hour
            ]
            
            # Calculate real-time metrics
            metrics = {
                "active_users": len(set(event["user_id"] for event in recent_data)),
                "interactions_per_minute": len(recent_data) / 60,
                "current_sessions": len(self.real_time_sessions.get(tenant_id, {})),
                "response_time_avg": self._calculate_avg_response_time(tenant_id),
                "success_rate": self._calculate_success_rate(recent_data),
                "popular_features": self._get_popular_features(recent_data),
                "system_health": await self._get_system_health(),
                "last_updated": current_time.isoformat()
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting real-time metrics: {str(e)}")
            return self._get_default_realtime_metrics()
    
    async def track_performance_metric(
        self,
        tenant_id: str,
        metric_name: str,
        value: float,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Track performance metrics"""
        
        try:
            metric_event = {
                "timestamp": datetime.utcnow(),
                "tenant_id": tenant_id,
                "metric_name": metric_name,
                "value": value,
                "metadata": metadata or {}
            }
            
            # Store performance metric
            if tenant_id not in self.performance_data:
                self.performance_data[tenant_id] = defaultdict(list)
            
            self.performance_data[tenant_id][metric_name].append(metric_event)
            
            # Keep only recent data (last 24 hours)
            cutoff_time = datetime.utcnow() - timedelta(days=1)
            self.performance_data[tenant_id][metric_name] = [
                event for event in self.performance_data[tenant_id][metric_name]
                if event["timestamp"] >= cutoff_time
            ]
            
        except Exception as e:
            logger.error(f"Error tracking performance metric: {str(e)}")
    
    async def get_performance_dashboard(
        self,
        tenant_id: str
    ) -> Dict[str, Any]:
        """Get performance dashboard data"""
        
        try:
            tenant_performance = self.performance_data.get(tenant_id, {})
            
            dashboard = {
                "response_times": self._analyze_response_times(tenant_performance.get("response_time", [])),
                "error_rates": self._analyze_error_rates(tenant_performance.get("error_rate", [])),
                "throughput": self._analyze_throughput(tenant_performance.get("throughput", [])),
                "user_satisfaction": self._analyze_satisfaction(tenant_performance.get("satisfaction", [])),
                "system_resources": await self._get_system_resources(),
                "alerts": await self._get_active_alerts(tenant_id),
                "recommendations": self._generate_performance_recommendations(tenant_performance)
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Error getting performance dashboard: {str(e)}")
            return self._get_default_performance_dashboard()
    
    def _get_session_id(self, user_id: str) -> str:
        """Get or create session ID for user"""
        # Simple session management - in production, use proper session store
        return f"session_{user_id}_{datetime.utcnow().strftime('%Y%m%d%H')}"
    
    async def _update_realtime_metrics(
        self,
        tenant_id: str,
        action: str,
        context: str
    ) -> None:
        """Update real-time metrics"""
        
        if tenant_id not in self.real_time_sessions:
            self.real_time_sessions[tenant_id] = {}
        
        # Update action counts
        action_key = f"action_{action}"
        if action_key not in self.real_time_sessions[tenant_id]:
            self.real_time_sessions[tenant_id][action_key] = 0
        self.real_time_sessions[tenant_id][action_key] += 1
        
        # Update context counts
        context_key = f"context_{context}"
        if context_key not in self.real_time_sessions[tenant_id]:
            self.real_time_sessions[tenant_id][context_key] = 0
        self.real_time_sessions[tenant_id][context_key] += 1
    
    def _calculate_popular_actions(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate most popular actions"""
        
        action_counts = defaultdict(int)
        for event in data:
            action_counts[event["action"]] += 1
        
        return [
            {"action": action, "count": count}
            for action, count in sorted(action_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        ]
    
    def _calculate_context_distribution(self, data: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate context distribution"""
        
        context_counts = defaultdict(int)
        for event in data:
            context_counts[event["context"]] += 1
        
        return dict(context_counts)
    
    def _calculate_hourly_usage(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate hourly usage patterns"""
        
        hourly_counts = defaultdict(int)
        for event in data:
            hour = event["timestamp"].hour
            hourly_counts[hour] += 1
        
        return [
            {"hour": hour, "count": hourly_counts[hour]}
            for hour in range(24)
        ]
    
    def _calculate_user_engagement(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate user engagement metrics"""
        
        if not data:
            return {"average_interactions": 0, "active_users": 0, "engagement_score": 0}
        
        user_interactions = defaultdict(int)
        for event in data:
            user_interactions[event["user_id"]] += 1
        
        total_users = len(user_interactions)
        total_interactions = sum(user_interactions.values())
        
        return {
            "average_interactions": total_interactions / total_users if total_users > 0 else 0,
            "active_users": total_users,
            "engagement_score": min(100, (total_interactions / total_users) * 10) if total_users > 0 else 0
        }
    
    async def _get_performance_metrics(
        self,
        tenant_id: str,
        timeframe: str
    ) -> Dict[str, Any]:
        """Get performance metrics for timeframe"""
        
        # Mock performance metrics
        return {
            "average_response_time": random.uniform(200, 800),
            "success_rate": random.uniform(0.95, 0.99),
            "error_rate": random.uniform(0.01, 0.05),
            "throughput": random.uniform(50, 200)
        }
    
    def _calculate_trends(self, data: List[Dict[str, Any]], timeframe: str) -> Dict[str, Any]:
        """Calculate usage trends"""
        
        if len(data) < 2:
            return {"trend": "stable", "change_percent": 0}
        
        # Simple trend calculation
        mid_point = len(data) // 2
        first_half = data[:mid_point]
        second_half = data[mid_point:]
        
        first_half_count = len(first_half)
        second_half_count = len(second_half)
        
        if first_half_count == 0:
            change_percent = 100
        else:
            change_percent = ((second_half_count - first_half_count) / first_half_count) * 100
        
        if change_percent > 10:
            trend = "increasing"
        elif change_percent < -10:
            trend = "decreasing"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "change_percent": round(change_percent, 2)
        }
    
    def _calculate_avg_response_time(self, tenant_id: str) -> float:
        """Calculate average response time"""
        
        performance_data = self.performance_data.get(tenant_id, {})
        response_times = performance_data.get("response_time", [])
        
        if not response_times:
            return 0.0
        
        recent_times = [event["value"] for event in response_times[-10:]]
        return sum(recent_times) / len(recent_times)
    
    def _calculate_success_rate(self, data: List[Dict[str, Any]]) -> float:
        """Calculate success rate from recent data"""
        
        if not data:
            return 1.0
        
        # Mock success rate calculation
        return random.uniform(0.95, 0.99)
    
    def _get_popular_features(self, data: List[Dict[str, Any]]) -> List[str]:
        """Get most popular features from recent data"""
        
        action_counts = defaultdict(int)
        for event in data:
            action_counts[event["action"]] += 1
        
        return [
            action for action, count in 
            sorted(action_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        ]
    
    async def _get_system_health(self) -> Dict[str, Any]:
        """Get system health metrics"""
        
        return {
            "status": "healthy",
            "cpu_usage": random.uniform(20, 80),
            "memory_usage": random.uniform(30, 70),
            "disk_usage": random.uniform(10, 50),
            "network_latency": random.uniform(10, 100)
        }
    
    def _analyze_response_times(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze response time metrics"""
        
        if not data:
            return {"average": 0, "p95": 0, "p99": 0}
        
        values = [event["value"] for event in data]
        values.sort()
        
        return {
            "average": sum(values) / len(values),
            "p95": values[int(len(values) * 0.95)] if values else 0,
            "p99": values[int(len(values) * 0.99)] if values else 0
        }
    
    def _analyze_error_rates(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze error rate metrics"""
        
        if not data:
            return {"current": 0, "trend": "stable"}
        
        recent_errors = [event["value"] for event in data[-10:]]
        current_rate = sum(recent_errors) / len(recent_errors)
        
        return {
            "current": current_rate,
            "trend": "stable"  # Simplified
        }
    
    def _analyze_throughput(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze throughput metrics"""
        
        if not data:
            return {"requests_per_second": 0, "trend": "stable"}
        
        recent_throughput = [event["value"] for event in data[-10:]]
        current_throughput = sum(recent_throughput) / len(recent_throughput)
        
        return {
            "requests_per_second": current_throughput,
            "trend": "stable"  # Simplified
        }
    
    def _analyze_satisfaction(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze user satisfaction metrics"""
        
        if not data:
            return {"score": 0, "trend": "stable"}
        
        recent_scores = [event["value"] for event in data[-10:]]
        current_score = sum(recent_scores) / len(recent_scores)
        
        return {
            "score": current_score,
            "trend": "stable"  # Simplified
        }
    
    async def _get_system_resources(self) -> Dict[str, Any]:
        """Get system resource usage"""
        
        return {
            "cpu": {"usage": random.uniform(20, 80), "cores": 8},
            "memory": {"usage": random.uniform(30, 70), "total_gb": 32},
            "disk": {"usage": random.uniform(10, 50), "total_gb": 500},
            "network": {"bandwidth_mbps": random.uniform(100, 1000)}
        }
    
    async def _get_active_alerts(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get active alerts for tenant"""
        
        # Mock alerts
        return [
            {
                "id": "alert_1",
                "severity": "warning",
                "message": "Response time above threshold",
                "timestamp": datetime.utcnow() - timedelta(minutes=15)
            }
        ]
    
    def _generate_performance_recommendations(
        self,
        performance_data: Dict[str, List[Dict[str, Any]]]
    ) -> List[str]:
        """Generate performance improvement recommendations"""
        
        recommendations = []
        
        # Analyze response times
        response_times = performance_data.get("response_time", [])
        if response_times:
            avg_response = sum(event["value"] for event in response_times) / len(response_times)
            if avg_response > 500:
                recommendations.append("Consider optimizing database queries to improve response times")
        
        # Analyze error rates
        error_rates = performance_data.get("error_rate", [])
        if error_rates:
            avg_error_rate = sum(event["value"] for event in error_rates) / len(error_rates)
            if avg_error_rate > 0.05:
                recommendations.append("Investigate and fix sources of errors to improve reliability")
        
        if not recommendations:
            recommendations.append("System performance is within acceptable ranges")
        
        return recommendations
    
    def _get_default_analytics(self) -> Dict[str, Any]:
        """Return default analytics when data is unavailable"""
        
        return {
            "total_interactions": 0,
            "unique_users": 0,
            "popular_actions": [],
            "context_distribution": {},
            "hourly_usage": [],
            "user_engagement": {"average_interactions": 0, "active_users": 0},
            "performance_metrics": {"average_response_time": 0, "success_rate": 1.0},
            "trends": {"trend": "stable", "change_percent": 0}
        }
    
    def _get_default_realtime_metrics(self) -> Dict[str, Any]:
        """Return default real-time metrics"""
        
        return {
            "active_users": 0,
            "interactions_per_minute": 0,
            "current_sessions": 0,
            "response_time_avg": 0,
            "success_rate": 1.0,
            "popular_features": [],
            "system_health": {"status": "unknown"},
            "last_updated": datetime.utcnow().isoformat()
        }
    
    def _get_default_performance_dashboard(self) -> Dict[str, Any]:
        """Return default performance dashboard"""
        
        return {
            "response_times": {"average": 0, "p95": 0, "p99": 0},
            "error_rates": {"current": 0, "trend": "stable"},
            "throughput": {"requests_per_second": 0, "trend": "stable"},
            "user_satisfaction": {"score": 0, "trend": "stable"},
            "system_resources": {"cpu": {"usage": 0}, "memory": {"usage": 0}},
            "alerts": [],
            "recommendations": ["No data available for analysis"]
        }
