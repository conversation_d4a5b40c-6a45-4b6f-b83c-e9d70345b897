"""
Pydantic schemas for CopilotKit API endpoints
"""

from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum


class ContextType(str, Enum):
    DASHBOARD = "dashboard"
    MAP = "map"
    REPORTS = "reports"
    ANALYTICS = "analytics"
    GENERAL = "general"
    ONBOARDING = "onboarding"


class AnalysisType(str, Enum):
    DEMOGRAPHIC = "demographic"
    COMPETITIVE = "competitive"
    COMPREHENSIVE = "comprehensive"


class ReportType(str, Enum):
    MARKET_OVERVIEW = "market_overview"
    COMPETITIVE_ANALYSIS = "competitive_analysis"
    DEMOGRAPHIC_STUDY = "demographic_study"
    OPPORTUNITY_ASSESSMENT = "opportunity_assessment"


class TimeframeType(str, Enum):
    CURRENT = "current"
    SIX_MONTHS = "6_months"
    TWELVE_MONTHS = "12_months"
    TWENTY_FOUR_MONTHS = "24_months"


class ModelType(str, Enum):
    GPT_4_TURBO = "gpt-4-turbo"
    GPT_4 = "gpt-4"
    CLAUDE_3_SONNET = "claude-3-sonnet"
    CLAUDE_3_OPUS = "claude-3-opus"


class ConversationMessage(BaseModel):
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Optional[Dict[str, Any]] = Field(default=None)


class CopilotRequest(BaseModel):
    message: str = Field(..., description="User message")
    context: ContextType = Field(default=ContextType.GENERAL, description="Context type")
    conversation_history: List[ConversationMessage] = Field(default=[], description="Previous conversation")
    tools: List[str] = Field(default=["all"], description="Available tools")
    model: ModelType = Field(default=ModelType.GPT_4_TURBO, description="AI model to use")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Response creativity")
    max_tokens: int = Field(default=4096, ge=1, le=8192, description="Maximum response tokens")
    stream: bool = Field(default=False, description="Enable streaming response")


class CopilotResponse(BaseModel):
    message: str = Field(..., description="AI response message")
    context: ContextType = Field(..., description="Response context")
    tools_used: List[str] = Field(default=[], description="Tools used in response")
    confidence: float = Field(default=0.8, ge=0.0, le=1.0, description="Response confidence")
    sources: List[str] = Field(default=[], description="Data sources used")
    suggestions: List[str] = Field(default=[], description="Follow-up suggestions")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")


class StreamingRequest(BaseModel):
    message: str = Field(..., description="User message")
    context: ContextType = Field(default=ContextType.GENERAL)
    model: ModelType = Field(default=ModelType.GPT_4_TURBO)
    conversation_id: Optional[str] = Field(default=None, description="Conversation ID for continuity")


class StreamingChunk(BaseModel):
    chunk: str = Field(..., description="Response chunk")
    is_complete: bool = Field(default=False, description="Whether response is complete")
    metadata: Optional[Dict[str, Any]] = Field(default=None)


class LocationCoordinate(BaseModel):
    latitude: float = Field(..., ge=-90, le=90, description="Latitude coordinate")
    longitude: float = Field(..., ge=-180, le=180, description="Longitude coordinate")


class AnalysisRequest(BaseModel):
    latitude: float = Field(..., ge=-90, le=90, description="Latitude coordinate")
    longitude: float = Field(..., ge=-180, le=180, description="Longitude coordinate")
    radius: float = Field(default=2.0, ge=0.1, le=50.0, description="Analysis radius in kilometers")
    analysis_type: AnalysisType = Field(default=AnalysisType.COMPREHENSIVE)
    context: ContextType = Field(default=ContextType.MAP)


class ReportRequest(BaseModel):
    location: LocationCoordinate = Field(..., description="Location for analysis")
    report_type: ReportType = Field(..., description="Type of report to generate")
    timeframe: TimeframeType = Field(default=TimeframeType.CURRENT)
    context: ContextType = Field(default=ContextType.REPORTS)
    custom_parameters: Optional[Dict[str, Any]] = Field(default=None)


class ActionRequest(BaseModel):
    action: str = Field(..., description="Action to perform")
    parameters: Dict[str, Any] = Field(default={}, description="Action parameters")
    context: ContextType = Field(default=ContextType.GENERAL)


class ActionResponse(BaseModel):
    success: bool = Field(..., description="Whether action was successful")
    action: str = Field(..., description="Action that was performed")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Action result data")
    error: Optional[str] = Field(default=None, description="Error message if failed")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")


class ConversationHistory(BaseModel):
    conversation_id: str = Field(..., description="Unique conversation identifier")
    messages: List[ConversationMessage] = Field(..., description="Conversation messages")
    metadata: Dict[str, Any] = Field(default={}, description="Conversation metadata")
    created_at: datetime = Field(..., description="Conversation creation time")
    updated_at: datetime = Field(..., description="Last update time")


class AIInsights(BaseModel):
    insights: List[str] = Field(..., description="Generated insights")
    recommendations: List[str] = Field(..., description="AI recommendations")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    sources: List[str] = Field(default=[], description="Data sources")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")


class LocationAnalysis(BaseModel):
    demographic_score: float = Field(..., description="Demographic suitability score")
    competition_level: str = Field(..., description="Competition level assessment")
    foot_traffic: int = Field(..., description="Estimated foot traffic")
    market_potential: str = Field(..., description="Market potential assessment")
    insights: List[str] = Field(default=[], description="Analysis insights")
    recommendations: List[str] = Field(default=[], description="Recommendations")
    confidence: float = Field(default=0.8, description="Analysis confidence")


class MarketReport(BaseModel):
    id: str = Field(..., description="Report unique identifier")
    report_type: ReportType = Field(..., description="Type of report")
    location: LocationCoordinate = Field(..., description="Report location")
    executive_summary: str = Field(..., description="Executive summary")
    key_findings: List[str] = Field(..., description="Key findings")
    recommendations: List[str] = Field(..., description="Recommendations")
    download_url: str = Field(..., description="Report download URL")
    created_at: datetime = Field(..., description="Report creation time")
    expires_at: Optional[datetime] = Field(default=None, description="Report expiration time")


class WebSocketMessage(BaseModel):
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(..., description="Message data")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    user_id: Optional[str] = Field(default=None)


class PerformanceMetrics(BaseModel):
    response_time: float = Field(..., description="Response time in milliseconds")
    tokens_used: int = Field(..., description="Number of tokens used")
    api_calls: int = Field(..., description="Number of API calls made")
    success_rate: float = Field(..., description="Success rate percentage")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthStatus(BaseModel):
    status: str = Field(..., description="Service health status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    features: Dict[str, bool] = Field(..., description="Available features")
    timestamp: datetime = Field(..., description="Health check timestamp")


class ErrorResponse(BaseModel):
    error: bool = Field(default=True, description="Error flag")
    message: str = Field(..., description="Error message")
    code: Optional[str] = Field(default=None, description="Error code")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ToolConfiguration(BaseModel):
    name: str = Field(..., description="Tool name")
    enabled: bool = Field(default=True, description="Whether tool is enabled")
    parameters: Dict[str, Any] = Field(default={}, description="Tool parameters")
    permissions: List[str] = Field(default=[], description="Required permissions")


class UserPreferences(BaseModel):
    language: str = Field(default="en", description="Preferred language")
    theme: str = Field(default="light", description="UI theme preference")
    ai_model: ModelType = Field(default=ModelType.GPT_4_TURBO, description="Preferred AI model")
    response_style: str = Field(default="balanced", description="AI response style")
    enable_voice: bool = Field(default=True, description="Enable voice features")
    enable_streaming: bool = Field(default=True, description="Enable streaming responses")


class TenantConfiguration(BaseModel):
    tenant_id: str = Field(..., description="Tenant identifier")
    plan: str = Field(..., description="Subscription plan")
    features: List[str] = Field(..., description="Available features")
    limits: Dict[str, int] = Field(..., description="Usage limits")
    settings: Dict[str, Any] = Field(default={}, description="Tenant settings")
