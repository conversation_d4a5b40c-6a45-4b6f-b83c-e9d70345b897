"""
Enhanced Security Middleware for CopilotKit Integration
Provides comprehensive security controls for <PERSON> assistant features
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from fastapi import HTTPException, Request, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response, JSONResponse
import json

from app.core.config import settings
from app.auth.auth_manager import auth_manager, rbac_manager

logger = logging.getLogger(__name__)


class CopilotKitSecurityMiddleware(BaseHTTPMiddleware):
    """Enhanced security middleware for CopilotKit endpoints"""
    
    def __init__(self, app, exclude_paths: Optional[List[str]] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or ["/health", "/metrics", "/docs", "/openapi.json"]
        self.rate_limits = {
            "chat": {"requests": 100, "window": 3600},  # 100 requests per hour
            "stream": {"requests": 50, "window": 3600},  # 50 streams per hour
            "analysis": {"requests": 20, "window": 3600},  # 20 analyses per hour
            "reports": {"requests": 10, "window": 3600}   # 10 reports per hour
        }
        self.user_sessions = {}
        self.blocked_ips = set()
        
    async def dispatch(self, request: Request, call_next) -> Response:
        """Process request with enhanced security checks"""
        
        # Skip security for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Only apply to CopilotKit endpoints
        if not request.url.path.startswith("/api/v1/copilotkit"):
            return await call_next(request)
        
        start_time = time.time()
        
        try:
            # Extract request information
            client_ip = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            
            # Check IP blocking
            if client_ip in self.blocked_ips:
                return self._create_security_response(
                    "IP address blocked due to suspicious activity",
                    status.HTTP_403_FORBIDDEN
                )
            
            # Validate request headers
            security_check = await self._validate_request_headers(request)
            if not security_check["valid"]:
                return self._create_security_response(
                    security_check["message"],
                    status.HTTP_400_BAD_REQUEST
                )
            
            # Extract user information from token
            user_info = await self._extract_user_info(request)
            if not user_info:
                return self._create_security_response(
                    "Authentication required",
                    status.HTTP_401_UNAUTHORIZED
                )
            
            # Check user permissions for CopilotKit features
            permission_check = await self._check_copilotkit_permissions(request, user_info)
            if not permission_check["allowed"]:
                return self._create_security_response(
                    permission_check["message"],
                    status.HTTP_403_FORBIDDEN
                )
            
            # Apply rate limiting
            rate_limit_check = await self._check_rate_limits(request, user_info)
            if not rate_limit_check["allowed"]:
                return self._create_rate_limit_response(rate_limit_check)
            
            # Validate request content
            content_check = await self._validate_request_content(request)
            if not content_check["valid"]:
                return self._create_security_response(
                    content_check["message"],
                    status.HTTP_400_BAD_REQUEST
                )
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            response = self._add_security_headers(response)
            
            # Log successful request
            await self._log_request(request, response, user_info, time.time() - start_time)
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {str(e)}")
            return self._create_security_response(
                "Security validation failed",
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    async def _validate_request_headers(self, request: Request) -> Dict[str, Any]:
        """Validate request headers for security"""
        
        # Check for required headers
        required_headers = ["authorization", "content-type"]
        for header in required_headers:
            if header not in request.headers:
                return {
                    "valid": False,
                    "message": f"Missing required header: {header}"
                }
        
        # Validate content type for POST requests
        if request.method == "POST":
            content_type = request.headers.get("content-type", "")
            if not content_type.startswith("application/json"):
                return {
                    "valid": False,
                    "message": "Invalid content type. Expected application/json"
                }
        
        # Check for suspicious headers
        suspicious_headers = ["x-forwarded-for", "x-real-ip"]
        for header in suspicious_headers:
            if header in request.headers:
                # Log potential proxy/forwarding
                logger.warning(f"Request with forwarding header: {header}")
        
        return {"valid": True, "message": "Headers valid"}
    
    async def _extract_user_info(self, request: Request) -> Optional[Dict[str, Any]]:
        """Extract user information from request"""
        
        try:
            auth_header = request.headers.get("authorization", "")
            if not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header[7:]  # Remove "Bearer "
            payload = auth_manager.verify_token(token)
            
            return {
                "user_id": payload.get("user_id"),
                "tenant_id": payload.get("tenant_id"),
                "role": payload.get("role", "user"),
                "permissions": payload.get("permissions", [])
            }
            
        except Exception as e:
            logger.warning(f"Failed to extract user info: {str(e)}")
            return None
    
    async def _check_copilotkit_permissions(
        self,
        request: Request,
        user_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Check user permissions for CopilotKit features"""
        
        # Define required permissions for different endpoints
        endpoint_permissions = {
            "/chat": "copilotkit:chat",
            "/stream": "copilotkit:stream",
            "/actions": "copilotkit:actions",
            "/analysis": "copilotkit:analysis",
            "/reports": "copilotkit:reports",
            "/websocket": "copilotkit:realtime"
        }
        
        # Find matching endpoint
        endpoint_path = None
        for path in endpoint_permissions:
            if request.url.path.endswith(path):
                endpoint_path = path
                break
        
        if not endpoint_path:
            return {"allowed": True, "message": "No specific permissions required"}
        
        required_permission = endpoint_permissions[endpoint_path]
        user_permissions = user_info.get("permissions", [])
        
        # Check if user has required permission
        if required_permission not in user_permissions:
            # Check role-based access as fallback
            user_role = user_info.get("role", "user")
            if user_role in ["admin", "manager"]:
                return {"allowed": True, "message": "Access granted via role"}
            
            return {
                "allowed": False,
                "message": f"Insufficient permissions. Required: {required_permission}"
            }
        
        return {"allowed": True, "message": "Permission granted"}
    
    async def _check_rate_limits(
        self,
        request: Request,
        user_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Check rate limits for user"""
        
        user_id = user_info.get("user_id")
        if not user_id:
            return {"allowed": False, "message": "User ID required for rate limiting"}
        
        # Determine endpoint type
        endpoint_type = "chat"  # default
        if "stream" in request.url.path:
            endpoint_type = "stream"
        elif "analysis" in request.url.path:
            endpoint_type = "analysis"
        elif "reports" in request.url.path:
            endpoint_type = "reports"
        
        # Get rate limit configuration
        rate_config = self.rate_limits.get(endpoint_type, self.rate_limits["chat"])
        
        # Check user's request history
        current_time = datetime.utcnow()
        window_start = current_time - timedelta(seconds=rate_config["window"])
        
        # Initialize user session if not exists
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = {}
        
        if endpoint_type not in self.user_sessions[user_id]:
            self.user_sessions[user_id][endpoint_type] = []
        
        # Clean old requests
        user_requests = self.user_sessions[user_id][endpoint_type]
        user_requests = [req_time for req_time in user_requests if req_time > window_start]
        self.user_sessions[user_id][endpoint_type] = user_requests
        
        # Check if limit exceeded
        if len(user_requests) >= rate_config["requests"]:
            return {
                "allowed": False,
                "message": f"Rate limit exceeded for {endpoint_type}",
                "retry_after": rate_config["window"] - (current_time - min(user_requests)).total_seconds()
            }
        
        # Add current request
        self.user_sessions[user_id][endpoint_type].append(current_time)
        
        return {
            "allowed": True,
            "remaining": rate_config["requests"] - len(user_requests) - 1
        }
    
    async def _validate_request_content(self, request: Request) -> Dict[str, Any]:
        """Validate request content for security"""
        
        if request.method not in ["POST", "PUT", "PATCH"]:
            return {"valid": True, "message": "No content validation needed"}
        
        try:
            # Check content length
            content_length = int(request.headers.get("content-length", 0))
            if content_length > settings.MAX_REQUEST_SIZE:
                return {
                    "valid": False,
                    "message": f"Request too large. Max size: {settings.MAX_REQUEST_SIZE} bytes"
                }
            
            # For JSON content, validate structure
            content_type = request.headers.get("content-type", "")
            if content_type.startswith("application/json"):
                # Note: In real implementation, you'd need to read and validate JSON
                # This is a simplified check
                pass
            
            return {"valid": True, "message": "Content valid"}
            
        except Exception as e:
            return {
                "valid": False,
                "message": f"Content validation error: {str(e)}"
            }
    
    def _add_security_headers(self, response: Response) -> Response:
        """Add security headers to response"""
        
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response
    
    async def _log_request(
        self,
        request: Request,
        response: Response,
        user_info: Dict[str, Any],
        response_time: float
    ) -> None:
        """Log request for security monitoring"""
        
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "method": request.method,
            "path": request.url.path,
            "user_id": user_info.get("user_id"),
            "tenant_id": user_info.get("tenant_id"),
            "ip_address": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent", ""),
            "status_code": response.status_code,
            "response_time_ms": response_time * 1000
        }
        
        logger.info(f"CopilotKit request: {json.dumps(log_data)}")
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        
        # Check for forwarded IP headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to client host
        return request.client.host if request.client else "unknown"
    
    def _create_security_response(self, message: str, status_code: int) -> JSONResponse:
        """Create standardized security error response"""
        
        return JSONResponse(
            status_code=status_code,
            content={
                "error": "Security validation failed",
                "message": message,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    def _create_rate_limit_response(self, rate_limit_info: Dict[str, Any]) -> JSONResponse:
        """Create rate limit error response"""
        
        headers = {}
        if "retry_after" in rate_limit_info:
            headers["Retry-After"] = str(int(rate_limit_info["retry_after"]))
        
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": "Rate limit exceeded",
                "message": rate_limit_info["message"],
                "retry_after_seconds": rate_limit_info.get("retry_after"),
                "timestamp": datetime.utcnow().isoformat()
            },
            headers=headers
        )
