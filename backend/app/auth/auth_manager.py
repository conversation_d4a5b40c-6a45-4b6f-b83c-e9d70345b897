"""
Enterprise Authentication & Authorization System
Implements JWT, RBAC, SSO, and multi-tenant user management
"""

import jwt
import bcrypt
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
from fastapi import HTTPException, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..core.database import get_db
from ..models.auth import User, Tenant, <PERSON>Key, Session as UserSession
from ..models.rbac import Role, Permission, UserRole
from ..core.config import settings
from ..core.redis_client import redis_client


class AuthenticationManager:
    """Handles JWT token generation, validation, and user authentication"""
    
    def __init__(self):
        self.access_token_expire = timedelta(minutes=15)
        self.refresh_token_expire = timedelta(days=7)
        self.security = HTTPBearer()
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def generate_tokens(self, user: User) -> Dict[str, str]:
        """Generate access and refresh tokens"""
        now = datetime.utcnow()
        
        # Access token payload
        access_payload = {
            'user_id': str(user.id),
            'tenant_id': str(user.tenant_id),
            'email': user.email,
            'role': user.role,
            'permissions': self._get_user_permissions(user),
            'iat': now,
            'exp': now + self.access_token_expire,
            'type': 'access'
        }
        
        # Refresh token payload
        refresh_payload = {
            'user_id': str(user.id),
            'tenant_id': str(user.tenant_id),
            'iat': now,
            'exp': now + self.refresh_token_expire,
            'type': 'refresh'
        }
        
        access_token = jwt.encode(access_payload, settings.JWT_SECRET, algorithm='HS256')
        refresh_token = jwt.encode(refresh_payload, settings.JWT_REFRESH_SECRET, algorithm='HS256')
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'bearer',
            'expires_in': int(self.access_token_expire.total_seconds())
        }
    
    def verify_token(self, token: str, token_type: str = 'access') -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            secret = settings.JWT_SECRET if token_type == 'access' else settings.JWT_REFRESH_SECRET
            payload = jwt.decode(token, secret, algorithms=['HS256'])
            
            # Check if token is revoked
            if self._is_token_revoked(payload.get('user_id'), token):
                raise HTTPException(status_code=401, detail="Token has been revoked")
            
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token has expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")
    
    def refresh_access_token(self, refresh_token: str, db: Session) -> Dict[str, str]:
        """Generate new access token using refresh token"""
        payload = self.verify_token(refresh_token, 'refresh')
        
        user = db.query(User).filter(User.id == payload['user_id']).first()
        if not user or not user.is_active:
            raise HTTPException(status_code=401, detail="User not found or inactive")
        
        return self.generate_tokens(user)
    
    def revoke_token(self, user_id: str, token: str):
        """Revoke a token by adding it to Redis blacklist"""
        redis_client.setex(f"revoked_token:{user_id}:{token}", 86400, "true")
    
    def _is_token_revoked(self, user_id: str, token: str) -> bool:
        """Check if token is in revocation list"""
        return redis_client.exists(f"revoked_token:{user_id}:{token}")
    
    def _get_user_permissions(self, user: User) -> List[str]:
        """Get user permissions based on roles"""
        # This will be implemented with RBAC system
        return []


class RBACManager:
    """Role-Based Access Control Manager"""
    
    def __init__(self):
        self.permission_cache = {}
    
    def has_permission(self, user_id: str, tenant_id: str, resource: str, action: str, db: Session) -> bool:
        """Check if user has permission for resource/action"""
        cache_key = f"perm:{user_id}:{resource}:{action}"
        
        # Check cache first
        if cache_key in self.permission_cache:
            return self.permission_cache[cache_key]
        
        # Query user roles and permissions
        user_roles = db.query(UserRole).filter(
            and_(UserRole.user_id == user_id, UserRole.tenant_id == tenant_id)
        ).all()
        
        has_perm = False
        for user_role in user_roles:
            role_permissions = db.query(Permission).join(Role).filter(
                and_(Role.id == user_role.role_id, Role.tenant_id == tenant_id)
            ).all()
            
            for permission in role_permissions:
                if (permission.resource == resource or permission.resource == '*') and \
                   (permission.action == action or permission.action == '*'):
                    has_perm = True
                    break
            
            if has_perm:
                break
        
        # Cache result for 5 minutes
        self.permission_cache[cache_key] = has_perm
        redis_client.setex(cache_key, 300, str(has_perm))
        
        return has_perm
    
    def get_user_permissions(self, user_id: str, tenant_id: str, db: Session) -> List[Dict[str, str]]:
        """Get all permissions for a user"""
        user_roles = db.query(UserRole).filter(
            and_(UserRole.user_id == user_id, UserRole.tenant_id == tenant_id)
        ).all()
        
        permissions = []
        for user_role in user_roles:
            role_permissions = db.query(Permission).join(Role).filter(
                and_(Role.id == user_role.role_id, Role.tenant_id == tenant_id)
            ).all()
            
            for perm in role_permissions:
                permissions.append({
                    'resource': perm.resource,
                    'action': perm.action,
                    'conditions': perm.conditions or {}
                })
        
        return permissions
    
    def assign_role(self, user_id: str, tenant_id: str, role_name: str, db: Session):
        """Assign role to user"""
        role = db.query(Role).filter(
            and_(Role.name == role_name, Role.tenant_id == tenant_id)
        ).first()
        
        if not role:
            raise HTTPException(status_code=404, detail="Role not found")
        
        # Check if user already has this role
        existing = db.query(UserRole).filter(
            and_(
                UserRole.user_id == user_id,
                UserRole.tenant_id == tenant_id,
                UserRole.role_id == role.id
            )
        ).first()
        
        if not existing:
            user_role = UserRole(
                user_id=user_id,
                tenant_id=tenant_id,
                role_id=role.id
            )
            db.add(user_role)
            db.commit()
        
        # Clear permission cache for user
        self._clear_user_permission_cache(user_id)
    
    def remove_role(self, user_id: str, tenant_id: str, role_name: str, db: Session):
        """Remove role from user"""
        role = db.query(Role).filter(
            and_(Role.name == role_name, Role.tenant_id == tenant_id)
        ).first()
        
        if role:
            db.query(UserRole).filter(
                and_(
                    UserRole.user_id == user_id,
                    UserRole.tenant_id == tenant_id,
                    UserRole.role_id == role.id
                )
            ).delete()
            db.commit()
            
            # Clear permission cache for user
            self._clear_user_permission_cache(user_id)
    
    def _clear_user_permission_cache(self, user_id: str):
        """Clear cached permissions for user"""
        pattern = f"perm:{user_id}:*"
        keys = redis_client.keys(pattern)
        if keys:
            redis_client.delete(*keys)


class APIKeyManager:
    """API Key Management for programmatic access"""
    
    def generate_api_key(self, tenant_id: str, name: str, permissions: List[str], db: Session) -> str:
        """Generate new API key"""
        key = f"bitebase_{secrets.token_urlsafe(32)}"
        
        api_key = APIKey(
            key_hash=self._hash_key(key),
            tenant_id=tenant_id,
            name=name,
            permissions=permissions,
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        db.add(api_key)
        db.commit()
        
        return key
    
    def validate_api_key(self, key: str, db: Session) -> Optional[APIKey]:
        """Validate API key and return key info"""
        key_hash = self._hash_key(key)
        
        api_key = db.query(APIKey).filter(
            and_(APIKey.key_hash == key_hash, APIKey.is_active == True)
        ).first()
        
        if api_key:
            # Update last used timestamp
            api_key.last_used = datetime.utcnow()
            db.commit()
            
            # Check rate limits
            if not self._check_rate_limit(api_key):
                raise HTTPException(status_code=429, detail="API rate limit exceeded")
        
        return api_key
    
    def revoke_api_key(self, key_id: str, tenant_id: str, db: Session):
        """Revoke API key"""
        api_key = db.query(APIKey).filter(
            and_(APIKey.id == key_id, APIKey.tenant_id == tenant_id)
        ).first()
        
        if api_key:
            api_key.is_active = False
            api_key.revoked_at = datetime.utcnow()
            db.commit()
    
    def _hash_key(self, key: str) -> str:
        """Hash API key for storage"""
        return bcrypt.hashpw(key.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def _check_rate_limit(self, api_key: APIKey) -> bool:
        """Check API key rate limits"""
        key = f"api_rate_limit:{api_key.id}"
        current = redis_client.get(key)
        
        if current is None:
            redis_client.setex(key, 3600, 1)  # 1 hour window
            return True
        
        if int(current) >= api_key.rate_limit:
            return False
        
        redis_client.incr(key)
        return True


class SSOManager:
    """Single Sign-On Manager for enterprise authentication"""
    
    def __init__(self):
        self.supported_providers = ['google', 'microsoft', 'okta', 'auth0']
    
    def initiate_sso_login(self, tenant_id: str, provider: str, redirect_uri: str) -> str:
        """Initiate SSO login flow"""
        if provider not in self.supported_providers:
            raise HTTPException(status_code=400, detail="Unsupported SSO provider")
        
        # Generate state parameter for security
        state = secrets.token_urlsafe(32)
        redis_client.setex(f"sso_state:{state}", 600, f"{tenant_id}:{provider}:{redirect_uri}")
        
        # Return authorization URL based on provider
        return self._get_authorization_url(provider, state, redirect_uri)
    
    def handle_sso_callback(self, code: str, state: str, db: Session) -> Dict[str, str]:
        """Handle SSO callback and create/login user"""
        # Verify state parameter
        state_data = redis_client.get(f"sso_state:{state}")
        if not state_data:
            raise HTTPException(status_code=400, detail="Invalid or expired state")
        
        tenant_id, provider, redirect_uri = state_data.decode().split(':')
        
        # Exchange code for user info
        user_info = self._exchange_code_for_user_info(provider, code)
        
        # Find or create user
        user = self._find_or_create_sso_user(tenant_id, user_info, db)
        
        # Generate tokens
        auth_manager = AuthenticationManager()
        return auth_manager.generate_tokens(user)
    
    def _get_authorization_url(self, provider: str, state: str, redirect_uri: str) -> str:
        """Get authorization URL for SSO provider"""
        # Implementation depends on specific SSO provider
        # This is a simplified example
        base_urls = {
            'google': 'https://accounts.google.com/oauth/authorize',
            'microsoft': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
            'okta': f'https://{settings.OKTA_DOMAIN}/oauth2/default/v1/authorize',
        }
        
        return f"{base_urls[provider]}?client_id={settings.SSO_CLIENT_ID}&response_type=code&scope=openid email profile&state={state}&redirect_uri={redirect_uri}"
    
    def _exchange_code_for_user_info(self, provider: str, code: str) -> Dict[str, Any]:
        """Exchange authorization code for user information"""
        # Implementation depends on specific SSO provider
        # This would make HTTP requests to provider's token and userinfo endpoints
        pass
    
    def _find_or_create_sso_user(self, tenant_id: str, user_info: Dict[str, Any], db: Session) -> User:
        """Find existing user or create new one from SSO info"""
        email = user_info.get('email')
        
        user = db.query(User).filter(
            and_(User.email == email, User.tenant_id == tenant_id)
        ).first()
        
        if not user:
            user = User(
                tenant_id=tenant_id,
                email=email,
                first_name=user_info.get('given_name', ''),
                last_name=user_info.get('family_name', ''),
                is_active=True,
                auth_provider='sso',
                created_at=datetime.utcnow()
            )
            db.add(user)
            db.commit()
            db.refresh(user)
        
        return user


# Initialize managers
auth_manager = AuthenticationManager()
rbac_manager = RBACManager()
api_key_manager = APIKeyManager()
sso_manager = SSOManager()


# Dependency functions for FastAPI
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(auth_manager.security),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user"""
    token = credentials.credentials
    payload = auth_manager.verify_token(token)
    
    user = db.query(User).filter(User.id == payload['user_id']).first()
    if not user or not user.is_active:
        raise HTTPException(status_code=401, detail="User not found or inactive")
    
    return user


async def get_current_tenant(user: User = Depends(get_current_user)) -> Tenant:
    """Get current user's tenant"""
    return user.tenant


def require_permission(resource: str, action: str):
    """Decorator to require specific permission"""
    def permission_checker(
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        if not rbac_manager.has_permission(str(user.id), str(user.tenant_id), resource, action, db):
            raise HTTPException(status_code=403, detail="Insufficient permissions")
        return user
    
    return permission_checker


async def validate_api_key_auth(
    request: Request,
    db: Session = Depends(get_db)
) -> APIKey:
    """Validate API key authentication"""
    api_key = request.headers.get('X-API-Key')
    if not api_key:
        raise HTTPException(status_code=401, detail="API key required")
    
    key_info = api_key_manager.validate_api_key(api_key, db)
    if not key_info:
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return key_info
