"""
Comprehensive Monitoring and Analytics for CopilotKit Integration
Provides real-time monitoring, performance tracking, and usage analytics
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import json
import psutil
import threading

from app.core.config import settings
from app.services.analytics_service import AnalyticsService

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Performance metric data structure"""
    timestamp: datetime
    metric_name: str
    value: float
    tags: Dict[str, str]
    tenant_id: Optional[str] = None
    user_id: Optional[str] = None


@dataclass
class SystemHealth:
    """System health status"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_latency: float
    active_connections: int
    error_rate: float
    response_time_avg: float


@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    metric: str
    threshold: float
    operator: str  # >, <, >=, <=, ==
    duration: int  # seconds
    severity: str  # critical, warning, info
    enabled: bool = True


class CopilotKitMonitor:
    """Comprehensive monitoring system for CopilotKit"""
    
    def __init__(self):
        self.analytics_service = AnalyticsService()
        self.metrics_buffer = deque(maxlen=10000)
        self.health_history = deque(maxlen=1000)
        self.alert_rules = []
        self.active_alerts = {}
        self.performance_cache = defaultdict(list)
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # Initialize default alert rules
        self._setup_default_alerts()
        
        # Start monitoring
        self.start_monitoring()
    
    def start_monitoring(self):
        """Start the monitoring system"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            logger.info("CopilotKit monitoring started")
    
    def stop_monitoring(self):
        """Stop the monitoring system"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("CopilotKit monitoring stopped")
    
    async def track_request(
        self,
        endpoint: str,
        method: str,
        user_id: str,
        tenant_id: str,
        response_time: float,
        status_code: int,
        error: Optional[str] = None
    ):
        """Track API request metrics"""
        
        try:
            # Create performance metric
            metric = PerformanceMetric(
                timestamp=datetime.utcnow(),
                metric_name="api_request",
                value=response_time,
                tags={
                    "endpoint": endpoint,
                    "method": method,
                    "status_code": str(status_code),
                    "error": error or "none"
                },
                tenant_id=tenant_id,
                user_id=user_id
            )
            
            # Add to buffer
            self.metrics_buffer.append(metric)
            
            # Track in analytics service
            await self.analytics_service.track_copilotkit_usage(
                user_id=user_id,
                tenant_id=tenant_id,
                action=f"{method}_{endpoint}",
                context="api_request",
                metadata={
                    "response_time": response_time,
                    "status_code": status_code,
                    "error": error
                }
            )
            
            # Track performance metric
            await self.analytics_service.track_performance_metric(
                tenant_id=tenant_id,
                metric_name="response_time",
                value=response_time,
                metadata={"endpoint": endpoint, "method": method}
            )
            
            # Check for alerts
            await self._check_alerts(metric)
            
        except Exception as e:
            logger.error(f"Error tracking request: {str(e)}")
    
    async def track_ai_interaction(
        self,
        user_id: str,
        tenant_id: str,
        interaction_type: str,
        model_used: str,
        tokens_used: int,
        response_time: float,
        success: bool
    ):
        """Track AI interaction metrics"""
        
        try:
            metric = PerformanceMetric(
                timestamp=datetime.utcnow(),
                metric_name="ai_interaction",
                value=response_time,
                tags={
                    "interaction_type": interaction_type,
                    "model": model_used,
                    "tokens": str(tokens_used),
                    "success": str(success)
                },
                tenant_id=tenant_id,
                user_id=user_id
            )
            
            self.metrics_buffer.append(metric)
            
            # Track AI-specific metrics
            await self.analytics_service.track_copilotkit_usage(
                user_id=user_id,
                tenant_id=tenant_id,
                action=interaction_type,
                context="ai_interaction",
                metadata={
                    "model": model_used,
                    "tokens_used": tokens_used,
                    "response_time": response_time,
                    "success": success
                }
            )
            
        except Exception as e:
            logger.error(f"Error tracking AI interaction: {str(e)}")
    
    async def track_websocket_connection(
        self,
        user_id: str,
        tenant_id: str,
        action: str,  # connect, disconnect, message
        duration: Optional[float] = None
    ):
        """Track WebSocket connection metrics"""
        
        try:
            metric = PerformanceMetric(
                timestamp=datetime.utcnow(),
                metric_name="websocket_event",
                value=duration or 0,
                tags={
                    "action": action,
                    "connection_type": "copilotkit"
                },
                tenant_id=tenant_id,
                user_id=user_id
            )
            
            self.metrics_buffer.append(metric)
            
            await self.analytics_service.track_copilotkit_usage(
                user_id=user_id,
                tenant_id=tenant_id,
                action=f"websocket_{action}",
                context="realtime",
                metadata={"duration": duration}
            )
            
        except Exception as e:
            logger.error(f"Error tracking WebSocket event: {str(e)}")
    
    async def get_system_health(self) -> SystemHealth:
        """Get current system health status"""
        
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Calculate network latency (simplified)
            network_latency = await self._measure_network_latency()
            
            # Get application metrics
            active_connections = len(getattr(self, 'active_websocket_connections', []))
            error_rate = await self._calculate_error_rate()
            response_time_avg = await self._calculate_avg_response_time()
            
            health = SystemHealth(
                timestamp=datetime.utcnow(),
                cpu_usage=cpu_percent,
                memory_usage=memory.percent,
                disk_usage=disk.percent,
                network_latency=network_latency,
                active_connections=active_connections,
                error_rate=error_rate,
                response_time_avg=response_time_avg
            )
            
            # Add to history
            self.health_history.append(health)
            
            return health
            
        except Exception as e:
            logger.error(f"Error getting system health: {str(e)}")
            return SystemHealth(
                timestamp=datetime.utcnow(),
                cpu_usage=0,
                memory_usage=0,
                disk_usage=0,
                network_latency=0,
                active_connections=0,
                error_rate=0,
                response_time_avg=0
            )
    
    async def get_performance_dashboard(self, tenant_id: str) -> Dict[str, Any]:
        """Get comprehensive performance dashboard data"""
        
        try:
            # Get system health
            system_health = await self.get_system_health()
            
            # Get usage analytics
            usage_analytics = await self.analytics_service.get_usage_analytics(
                tenant_id=tenant_id,
                timeframe="24h"
            )
            
            # Get real-time metrics
            realtime_metrics = await self.analytics_service.get_realtime_metrics(tenant_id)
            
            # Get performance trends
            performance_trends = await self._get_performance_trends(tenant_id)
            
            # Get active alerts
            active_alerts = await self._get_active_alerts(tenant_id)
            
            dashboard = {
                "system_health": asdict(system_health),
                "usage_analytics": usage_analytics,
                "realtime_metrics": realtime_metrics,
                "performance_trends": performance_trends,
                "active_alerts": active_alerts,
                "recommendations": await self._generate_recommendations(tenant_id),
                "last_updated": datetime.utcnow().isoformat()
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Error getting performance dashboard: {str(e)}")
            return {"error": str(e), "last_updated": datetime.utcnow().isoformat()}
    
    async def create_alert_rule(self, rule: AlertRule) -> bool:
        """Create a new alert rule"""
        
        try:
            self.alert_rules.append(rule)
            logger.info(f"Created alert rule: {rule.name}")
            return True
        except Exception as e:
            logger.error(f"Error creating alert rule: {str(e)}")
            return False
    
    async def get_metrics_export(
        self,
        tenant_id: str,
        start_time: datetime,
        end_time: datetime,
        format: str = "json"
    ) -> Dict[str, Any]:
        """Export metrics for external analysis"""
        
        try:
            # Filter metrics by tenant and time range
            filtered_metrics = [
                metric for metric in self.metrics_buffer
                if (metric.tenant_id == tenant_id and
                    start_time <= metric.timestamp <= end_time)
            ]
            
            if format == "json":
                return {
                    "tenant_id": tenant_id,
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "metrics_count": len(filtered_metrics),
                    "metrics": [asdict(metric) for metric in filtered_metrics]
                }
            else:
                # Could implement CSV, Prometheus, etc.
                return {"error": f"Unsupported format: {format}"}
                
        except Exception as e:
            logger.error(f"Error exporting metrics: {str(e)}")
            return {"error": str(e)}
    
    def _monitoring_loop(self):
        """Main monitoring loop running in background thread"""
        
        while self.monitoring_active:
            try:
                # Collect system health
                asyncio.run(self.get_system_health())
                
                # Clean old metrics
                self._cleanup_old_metrics()
                
                # Check alert rules
                asyncio.run(self._check_all_alerts())
                
                # Sleep for monitoring interval
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                time.sleep(60)  # Wait longer on error
    
    def _setup_default_alerts(self):
        """Setup default alert rules"""
        
        default_rules = [
            AlertRule(
                name="High CPU Usage",
                metric="cpu_usage",
                threshold=80.0,
                operator=">=",
                duration=300,  # 5 minutes
                severity="warning"
            ),
            AlertRule(
                name="High Memory Usage",
                metric="memory_usage",
                threshold=85.0,
                operator=">=",
                duration=300,
                severity="warning"
            ),
            AlertRule(
                name="High Error Rate",
                metric="error_rate",
                threshold=5.0,
                operator=">=",
                duration=60,  # 1 minute
                severity="critical"
            ),
            AlertRule(
                name="Slow Response Time",
                metric="response_time_avg",
                threshold=2000.0,  # 2 seconds
                operator=">=",
                duration=180,  # 3 minutes
                severity="warning"
            )
        ]
        
        self.alert_rules.extend(default_rules)
    
    async def _check_alerts(self, metric: PerformanceMetric):
        """Check if metric triggers any alerts"""
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            if rule.metric == metric.metric_name:
                await self._evaluate_alert_rule(rule, metric)
    
    async def _check_all_alerts(self):
        """Check all alert rules against current system state"""
        
        try:
            system_health = await self.get_system_health()
            
            # Create metrics from system health
            health_metrics = [
                PerformanceMetric(
                    timestamp=system_health.timestamp,
                    metric_name="cpu_usage",
                    value=system_health.cpu_usage,
                    tags={}
                ),
                PerformanceMetric(
                    timestamp=system_health.timestamp,
                    metric_name="memory_usage",
                    value=system_health.memory_usage,
                    tags={}
                ),
                PerformanceMetric(
                    timestamp=system_health.timestamp,
                    metric_name="error_rate",
                    value=system_health.error_rate,
                    tags={}
                ),
                PerformanceMetric(
                    timestamp=system_health.timestamp,
                    metric_name="response_time_avg",
                    value=system_health.response_time_avg,
                    tags={}
                )
            ]
            
            for metric in health_metrics:
                await self._check_alerts(metric)
                
        except Exception as e:
            logger.error(f"Error checking all alerts: {str(e)}")
    
    async def _evaluate_alert_rule(self, rule: AlertRule, metric: PerformanceMetric):
        """Evaluate if a metric triggers an alert rule"""
        
        try:
            # Check if value meets threshold
            triggered = False
            if rule.operator == ">=":
                triggered = metric.value >= rule.threshold
            elif rule.operator == ">":
                triggered = metric.value > rule.threshold
            elif rule.operator == "<=":
                triggered = metric.value <= rule.threshold
            elif rule.operator == "<":
                triggered = metric.value < rule.threshold
            elif rule.operator == "==":
                triggered = metric.value == rule.threshold
            
            if triggered:
                # Check if alert is already active
                alert_key = f"{rule.name}_{metric.tenant_id or 'system'}"
                
                if alert_key not in self.active_alerts:
                    # Create new alert
                    alert = {
                        "rule_name": rule.name,
                        "metric": rule.metric,
                        "value": metric.value,
                        "threshold": rule.threshold,
                        "severity": rule.severity,
                        "triggered_at": metric.timestamp,
                        "tenant_id": metric.tenant_id
                    }
                    
                    self.active_alerts[alert_key] = alert
                    logger.warning(f"Alert triggered: {rule.name} - {metric.value} {rule.operator} {rule.threshold}")
                    
                    # Send alert notification (implement as needed)
                    await self._send_alert_notification(alert)
            else:
                # Clear alert if it was active
                alert_key = f"{rule.name}_{metric.tenant_id or 'system'}"
                if alert_key in self.active_alerts:
                    del self.active_alerts[alert_key]
                    logger.info(f"Alert cleared: {rule.name}")
                    
        except Exception as e:
            logger.error(f"Error evaluating alert rule: {str(e)}")
    
    async def _send_alert_notification(self, alert: Dict[str, Any]):
        """Send alert notification (implement based on requirements)"""
        
        # This could send emails, Slack messages, webhooks, etc.
        logger.critical(f"ALERT: {alert['rule_name']} - Value: {alert['value']}, Threshold: {alert['threshold']}")
    
    async def _measure_network_latency(self) -> float:
        """Measure network latency (simplified implementation)"""
        
        # In a real implementation, this would ping external services
        return 50.0  # Mock latency in ms
    
    async def _calculate_error_rate(self) -> float:
        """Calculate current error rate"""
        
        try:
            recent_metrics = [
                m for m in self.metrics_buffer
                if (m.metric_name == "api_request" and
                    m.timestamp > datetime.utcnow() - timedelta(minutes=5))
            ]
            
            if not recent_metrics:
                return 0.0
            
            error_count = sum(
                1 for m in recent_metrics
                if m.tags.get("status_code", "200").startswith(("4", "5"))
            )
            
            return (error_count / len(recent_metrics)) * 100
            
        except Exception:
            return 0.0
    
    async def _calculate_avg_response_time(self) -> float:
        """Calculate average response time"""
        
        try:
            recent_metrics = [
                m for m in self.metrics_buffer
                if (m.metric_name == "api_request" and
                    m.timestamp > datetime.utcnow() - timedelta(minutes=5))
            ]
            
            if not recent_metrics:
                return 0.0
            
            total_time = sum(m.value for m in recent_metrics)
            return total_time / len(recent_metrics)
            
        except Exception:
            return 0.0
    
    async def _get_performance_trends(self, tenant_id: str) -> Dict[str, Any]:
        """Get performance trends for tenant"""
        
        # Mock implementation - in production, analyze historical data
        return {
            "response_time_trend": "stable",
            "error_rate_trend": "decreasing",
            "usage_trend": "increasing",
            "performance_score": 85
        }
    
    async def _get_active_alerts(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get active alerts for tenant"""
        
        return [
            alert for alert in self.active_alerts.values()
            if alert.get("tenant_id") == tenant_id or alert.get("tenant_id") is None
        ]
    
    async def _generate_recommendations(self, tenant_id: str) -> List[str]:
        """Generate performance recommendations"""
        
        recommendations = []
        
        # Analyze recent performance
        system_health = await self.get_system_health()
        
        if system_health.cpu_usage > 70:
            recommendations.append("Consider scaling up CPU resources")
        
        if system_health.memory_usage > 80:
            recommendations.append("Memory usage is high - consider optimization")
        
        if system_health.response_time_avg > 1000:
            recommendations.append("Response times are slow - investigate bottlenecks")
        
        if not recommendations:
            recommendations.append("System performance is within normal parameters")
        
        return recommendations
    
    def _cleanup_old_metrics(self):
        """Clean up old metrics to prevent memory issues"""
        
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        
        # Clean metrics buffer
        self.metrics_buffer = deque(
            [m for m in self.metrics_buffer if m.timestamp > cutoff_time],
            maxlen=10000
        )
        
        # Clean health history
        self.health_history = deque(
            [h for h in self.health_history if h.timestamp > cutoff_time],
            maxlen=1000
        )


# Global monitor instance
copilotkit_monitor = CopilotKitMonitor()
