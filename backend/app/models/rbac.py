"""
Role-Based Access Control (RBAC) Models
Enterprise-grade permission system with multi-tenant support
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey, JSON, Index, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from ..core.database import Base


class Role(Base):
    """Roles define sets of permissions for users"""
    __tablename__ = "roles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    
    # Role information
    name = Column(String(100), nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Role properties
    is_system_role = Column(Boolean, default=False)  # System roles cannot be deleted
    is_default = Column(Boolean, default=False)  # Assigned to new users by default
    priority = Column(Integer, default=0)  # Higher priority roles override lower ones
    
    # Role inheritance
    inherits_from = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=True)
    
    # Metadata
    metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="roles")
    permissions = relationship("Permission", back_populates="role", cascade="all, delete-orphan")
    user_roles = relationship("UserRole", back_populates="role", cascade="all, delete-orphan")
    parent_role = relationship("Role", remote_side=[id], backref="child_roles")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('tenant_id', 'name', name='uq_roles_tenant_name'),
        Index('idx_roles_tenant_id', 'tenant_id'),
        Index('idx_roles_name', 'name'),
        Index('idx_roles_is_default', 'is_default'),
    )
    
    def __repr__(self):
        return f"<Role(id={self.id}, name={self.name}, tenant_id={self.tenant_id})>"


class Permission(Base):
    """Individual permissions that can be assigned to roles"""
    __tablename__ = "permissions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id", ondelete="CASCADE"), nullable=False)
    
    # Permission definition
    resource = Column(String(100), nullable=False)  # e.g., 'restaurants', 'reports', 'users'
    action = Column(String(50), nullable=False)     # e.g., 'create', 'read', 'update', 'delete', '*'
    
    # Optional conditions for fine-grained control
    conditions = Column(JSON, default=dict)  # e.g., {'owner_only': True, 'status': 'active'}
    
    # Permission metadata
    description = Column(Text, nullable=True)
    is_system_permission = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    role = relationship("Role", back_populates="permissions")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('role_id', 'resource', 'action', name='uq_permissions_role_resource_action'),
        Index('idx_permissions_role_id', 'role_id'),
        Index('idx_permissions_resource', 'resource'),
        Index('idx_permissions_action', 'action'),
    )
    
    def __repr__(self):
        return f"<Permission(id={self.id}, resource={self.resource}, action={self.action})>"


class UserRole(Base):
    """Many-to-many relationship between users and roles"""
    __tablename__ = "user_roles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id", ondelete="CASCADE"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    
    # Assignment metadata
    assigned_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Optional expiration
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="user_roles")
    role = relationship("Role", back_populates="user_roles")
    assigner = relationship("User", foreign_keys=[assigned_by])
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'role_id', 'tenant_id', name='uq_user_roles_user_role_tenant'),
        Index('idx_user_roles_user_id', 'user_id'),
        Index('idx_user_roles_role_id', 'role_id'),
        Index('idx_user_roles_tenant_id', 'tenant_id'),
        Index('idx_user_roles_expires_at', 'expires_at'),
    )
    
    def __repr__(self):
        return f"<UserRole(id={self.id}, user_id={self.user_id}, role_id={self.role_id})>"


class ResourcePermission(Base):
    """Resource-specific permissions for fine-grained access control"""
    __tablename__ = "resource_permissions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    
    # Resource identification
    resource_type = Column(String(100), nullable=False)  # e.g., 'restaurant', 'report'
    resource_id = Column(UUID(as_uuid=True), nullable=False)  # ID of the specific resource
    
    # Permission details
    permission = Column(String(50), nullable=False)  # e.g., 'read', 'write', 'admin'
    
    # Grant metadata
    granted_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    granted_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Optional expiration
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    granter = relationship("User", foreign_keys=[granted_by])
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'resource_type', 'resource_id', 'permission', 
                        name='uq_resource_permissions_user_resource_permission'),
        Index('idx_resource_permissions_user_id', 'user_id'),
        Index('idx_resource_permissions_resource', 'resource_type', 'resource_id'),
        Index('idx_resource_permissions_tenant_id', 'tenant_id'),
        Index('idx_resource_permissions_expires_at', 'expires_at'),
    )
    
    def __repr__(self):
        return f"<ResourcePermission(id={self.id}, user_id={self.user_id}, resource_type={self.resource_type})>"


class PermissionGroup(Base):
    """Groups of permissions for easier management"""
    __tablename__ = "permission_groups"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    
    # Group information
    name = Column(String(100), nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Group properties
    is_system_group = Column(Boolean, default=False)
    color = Column(String(7), nullable=True)  # Hex color for UI
    icon = Column(String(50), nullable=True)  # Icon name for UI
    
    # Permissions in this group
    permissions = Column(JSON, default=list)  # List of permission objects
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('tenant_id', 'name', name='uq_permission_groups_tenant_name'),
        Index('idx_permission_groups_tenant_id', 'tenant_id'),
        Index('idx_permission_groups_name', 'name'),
    )
    
    def __repr__(self):
        return f"<PermissionGroup(id={self.id}, name={self.name}, tenant_id={self.tenant_id})>"


class AuditLog(Base):
    """Audit log for permission and role changes"""
    __tablename__ = "audit_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    
    # Actor information
    actor_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    actor_type = Column(String(50), default='user')  # user, system, api_key
    
    # Action details
    action = Column(String(100), nullable=False)  # e.g., 'role_assigned', 'permission_granted'
    resource_type = Column(String(100), nullable=False)  # e.g., 'user', 'role', 'permission'
    resource_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Target information (who/what was affected)
    target_type = Column(String(100), nullable=True)
    target_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Change details
    old_values = Column(JSON, default=dict)
    new_values = Column(JSON, default=dict)
    
    # Request context
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Additional metadata
    metadata = Column(JSON, default=dict)
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    actor = relationship("User", foreign_keys=[actor_id])
    
    # Indexes
    __table_args__ = (
        Index('idx_audit_logs_tenant_id', 'tenant_id'),
        Index('idx_audit_logs_actor_id', 'actor_id'),
        Index('idx_audit_logs_action', 'action'),
        Index('idx_audit_logs_resource', 'resource_type', 'resource_id'),
        Index('idx_audit_logs_target', 'target_type', 'target_id'),
        Index('idx_audit_logs_created_at', 'created_at'),
    )
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, action={self.action}, actor_id={self.actor_id})>"
