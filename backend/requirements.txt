# BiteBase Intelligence Backend Dependencies

# Core Framework
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Database & ORM
sqlalchemy>=2.0.0
alembic>=1.10.0
aiosqlite>=0.19.0
psycopg2-binary>=2.9.0
asyncpg>=0.28.0
geoalchemy2>=0.14.0
pymongo>=4.5.0
redis>=4.5.0

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.0
python-multipart>=0.0.5
bcrypt>=4.0.0

# AI/ML & Analytics (Enhanced for CopilotKit)
openai>=1.0.0
anthropic>=0.20.0
langchain>=0.1.0
langchain-openai>=0.1.0
langchain-anthropic>=0.1.0
langgraph>=0.1.0
langsmith>=0.1.0
scikit-learn>=1.3.0
pandas>=2.0.0
numpy>=1.24.0
tiktoken>=0.5.0

# CopilotKit Backend Support
websockets>=11.0.0
sse-starlette>=1.6.0
celery[redis]>=5.3.0

# Vector Database & Embeddings
qdrant-client>=1.7.0
sentence-transformers>=2.2.0
chromadb>=0.4.0

# Monitoring & Observability
prometheus-client>=0.17.0
structlog>=23.1.0
opentelemetry-api>=1.18.0
opentelemetry-sdk>=1.18.0
opentelemetry-instrumentation-fastapi>=0.39b0

# Geospatial & Mapping
geoip2>=4.7.0
folium>=0.14.0
shapely>=2.0.0
geopy>=2.3.0

# HTTP & API
httpx>=0.24.0
aiohttp>=3.8.0
requests>=2.28.0

# Data Processing
python-dateutil>=2.8.0
pytz>=2023.3
python-dotenv>=1.0.0
pyyaml>=6.0.0

# Development
pytest>=7.0.0
pytest-asyncio>=0.20.0