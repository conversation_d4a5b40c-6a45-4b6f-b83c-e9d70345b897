// Polyfills for Jest testing environment

// TextEncoder/TextDecoder polyfill
const { TextEncoder, TextDecoder } = require('util')
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Blob polyfill
global.Blob = class Blob {
  constructor(parts = [], options = {}) {
    this.parts = parts
    this.type = options.type || ''
    this.size = parts.reduce((size, part) => size + (part.length || 0), 0)
  }
  
  text() {
    return Promise.resolve(this.parts.join(''))
  }
  
  arrayBuffer() {
    return Promise.resolve(new ArrayBuffer(this.size))
  }
}

// File polyfill
global.File = class File extends global.Blob {
  constructor(parts, name, options = {}) {
    super(parts, options)
    this.name = name
    this.lastModified = options.lastModified || Date.now()
  }
}

// FormData polyfill
global.FormData = class FormData {
  constructor() {
    this.data = new Map()
  }
  
  append(name, value) {
    if (!this.data.has(name)) {
      this.data.set(name, [])
    }
    this.data.get(name).push(value)
  }
  
  get(name) {
    const values = this.data.get(name)
    return values ? values[0] : null
  }
  
  getAll(name) {
    return this.data.get(name) || []
  }
  
  has(name) {
    return this.data.has(name)
  }
  
  set(name, value) {
    this.data.set(name, [value])
  }
  
  delete(name) {
    this.data.delete(name)
  }
  
  entries() {
    const entries = []
    for (const [name, values] of this.data) {
      for (const value of values) {
        entries.push([name, value])
      }
    }
    return entries[Symbol.iterator]()
  }
  
  keys() {
    return this.data.keys()
  }
  
  values() {
    const values = []
    for (const valueArray of this.data.values()) {
      values.push(...valueArray)
    }
    return values[Symbol.iterator]()
  }
  
  [Symbol.iterator]() {
    return this.entries()
  }
}

// Headers polyfill
global.Headers = class Headers {
  constructor(init = {}) {
    this.map = new Map()
    if (init) {
      if (init instanceof Headers) {
        for (const [name, value] of init.entries()) {
          this.append(name, value)
        }
      } else if (Array.isArray(init)) {
        for (const [name, value] of init) {
          this.append(name, value)
        }
      } else {
        for (const [name, value] of Object.entries(init)) {
          this.append(name, value)
        }
      }
    }
  }
  
  append(name, value) {
    const normalizedName = name.toLowerCase()
    const existing = this.map.get(normalizedName)
    this.map.set(normalizedName, existing ? `${existing}, ${value}` : value)
  }
  
  delete(name) {
    this.map.delete(name.toLowerCase())
  }
  
  get(name) {
    return this.map.get(name.toLowerCase()) || null
  }
  
  has(name) {
    return this.map.has(name.toLowerCase())
  }
  
  set(name, value) {
    this.map.set(name.toLowerCase(), value)
  }
  
  entries() {
    return this.map.entries()
  }
  
  keys() {
    return this.map.keys()
  }
  
  values() {
    return this.map.values()
  }
  
  [Symbol.iterator]() {
    return this.entries()
  }
}

// Request polyfill
global.Request = class Request {
  constructor(input, init = {}) {
    this.url = typeof input === 'string' ? input : input.url
    this.method = init.method || 'GET'
    this.headers = new Headers(init.headers)
    this.body = init.body || null
    this.mode = init.mode || 'cors'
    this.credentials = init.credentials || 'same-origin'
    this.cache = init.cache || 'default'
    this.redirect = init.redirect || 'follow'
    this.referrer = init.referrer || 'about:client'
    this.integrity = init.integrity || ''
  }
  
  clone() {
    return new Request(this.url, {
      method: this.method,
      headers: this.headers,
      body: this.body,
      mode: this.mode,
      credentials: this.credentials,
      cache: this.cache,
      redirect: this.redirect,
      referrer: this.referrer,
      integrity: this.integrity,
    })
  }
  
  text() {
    return Promise.resolve(this.body || '')
  }
  
  json() {
    return this.text().then(text => JSON.parse(text))
  }
}

// Response polyfill
global.Response = class Response {
  constructor(body = null, init = {}) {
    this.body = body
    this.status = init.status || 200
    this.statusText = init.statusText || 'OK'
    this.headers = new Headers(init.headers)
    this.ok = this.status >= 200 && this.status < 300
    this.redirected = false
    this.type = 'basic'
    this.url = ''
  }
  
  clone() {
    return new Response(this.body, {
      status: this.status,
      statusText: this.statusText,
      headers: this.headers,
    })
  }
  
  text() {
    return Promise.resolve(this.body || '')
  }
  
  json() {
    return this.text().then(text => JSON.parse(text))
  }
  
  arrayBuffer() {
    return Promise.resolve(new ArrayBuffer(0))
  }
  
  blob() {
    return Promise.resolve(new Blob([this.body || '']))
  }
  
  static error() {
    const response = new Response(null, { status: 0, statusText: '' })
    response.type = 'error'
    return response
  }
  
  static redirect(url, status = 302) {
    return new Response(null, {
      status,
      headers: { Location: url },
    })
  }
}

// AbortController polyfill
global.AbortController = class AbortController {
  constructor() {
    this.signal = new AbortSignal()
  }
  
  abort() {
    this.signal.aborted = true
    this.signal.dispatchEvent(new Event('abort'))
  }
}

global.AbortSignal = class AbortSignal {
  constructor() {
    this.aborted = false
    this.onabort = null
    this.addEventListener = jest.fn()
    this.removeEventListener = jest.fn()
    this.dispatchEvent = jest.fn()
  }
}

// EventSource polyfill
global.EventSource = class EventSource {
  constructor(url) {
    this.url = url
    this.readyState = EventSource.CONNECTING
    this.onopen = null
    this.onmessage = null
    this.onerror = null
    
    setTimeout(() => {
      this.readyState = EventSource.OPEN
      if (this.onopen) this.onopen()
    }, 100)
  }
  
  static CONNECTING = 0
  static OPEN = 1
  static CLOSED = 2
  
  close() {
    this.readyState = EventSource.CLOSED
  }
  
  addEventListener = jest.fn()
  removeEventListener = jest.fn()
}

// MessageChannel polyfill
global.MessageChannel = class MessageChannel {
  constructor() {
    this.port1 = new MessagePort()
    this.port2 = new MessagePort()
  }
}

global.MessagePort = class MessagePort {
  constructor() {
    this.onmessage = null
  }
  
  postMessage = jest.fn()
  start = jest.fn()
  close = jest.fn()
  addEventListener = jest.fn()
  removeEventListener = jest.fn()
}

// CustomEvent polyfill
global.CustomEvent = class CustomEvent extends Event {
  constructor(type, options = {}) {
    super(type, options)
    this.detail = options.detail
  }
}
