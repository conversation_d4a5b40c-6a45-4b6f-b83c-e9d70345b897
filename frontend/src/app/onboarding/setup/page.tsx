'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { 
  Store, 
  MapPin, 
  Settings, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Users,
  Calendar,
  Globe
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface SetupStep {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  component: React.ComponentType<any>;
}

// Mock setup step components
const RestaurantProfileStep = ({ onNext, data, setData }: any) => (
  <div className="space-y-6">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Restaurant Name
      </label>
      <input
        type="text"
        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
        placeholder="Enter your restaurant name"
        value={data.name || ''}
        onChange={(e) => setData({ ...data, name: e.target.value })}
      />
    </div>
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Cuisine Type
      </label>
      <select
        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
        value={data.cuisine || ''}
        onChange={(e) => setData({ ...data, cuisine: e.target.value })}
      >
        <option value="">Select cuisine type</option>
        <option value="italian">Italian</option>
        <option value="asian">Asian</option>
        <option value="american">American</option>
        <option value="mediterranean">Mediterranean</option>
        <option value="other">Other</option>
      </select>
    </div>
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Restaurant Size
      </label>
      <select
        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
        value={data.size || ''}
        onChange={(e) => setData({ ...data, size: e.target.value })}
      >
        <option value="">Select restaurant size</option>
        <option value="small">Small (1-50 seats)</option>
        <option value="medium">Medium (51-150 seats)</option>
        <option value="large">Large (151+ seats)</option>
      </select>
    </div>
    <button
      onClick={onNext}
      disabled={!data.name || !data.cuisine || !data.size}
      className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 text-white py-3 rounded-lg font-semibold transition-colors"
    >
      Continue
    </button>
  </div>
);

const LocationSetupStep = ({ onNext, onPrevious, data, setData }: any) => (
  <div className="space-y-6">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Street Address
      </label>
      <input
        type="text"
        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
        placeholder="Enter street address"
        value={data.address || ''}
        onChange={(e) => setData({ ...data, address: e.target.value })}
      />
    </div>
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          City
        </label>
        <input
          type="text"
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          placeholder="City"
          value={data.city || ''}
          onChange={(e) => setData({ ...data, city: e.target.value })}
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          ZIP Code
        </label>
        <input
          type="text"
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          placeholder="ZIP Code"
          value={data.zip || ''}
          onChange={(e) => setData({ ...data, zip: e.target.value })}
        />
      </div>
    </div>
    <div className="flex space-x-4">
      <button
        onClick={onPrevious}
        className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg font-semibold transition-colors"
      >
        <ArrowLeft className="h-5 w-5 inline mr-2" />
        Previous
      </button>
      <button
        onClick={onNext}
        disabled={!data.address || !data.city || !data.zip}
        className="flex-1 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 text-white py-3 rounded-lg font-semibold transition-colors"
      >
        Continue
        <ArrowRight className="h-5 w-5 inline ml-2" />
      </button>
    </div>
  </div>
);

const PreferencesStep = ({ onNext, onPrevious, data, setData }: any) => (
  <div className="space-y-6">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-4">
        Primary Business Goals (Select all that apply)
      </label>
      <div className="space-y-3">
        {[
          'Increase Revenue',
          'Optimize Menu',
          'Improve Customer Experience',
          'Reduce Costs',
          'Expand Locations'
        ].map((goal) => (
          <label key={goal} className="flex items-center space-x-3">
            <input
              type="checkbox"
              className="h-5 w-5 text-orange-500 rounded border-gray-300 focus:ring-orange-500"
              checked={data.goals?.includes(goal) || false}
              onChange={(e) => {
                const goals = data.goals || [];
                if (e.target.checked) {
                  setData({ ...data, goals: [...goals, goal] });
                } else {
                  setData({ ...data, goals: goals.filter((g: string) => g !== goal) });
                }
              }}
            />
            <span className="text-gray-700">{goal}</span>
          </label>
        ))}
      </div>
    </div>
    <div className="flex space-x-4">
      <button
        onClick={onPrevious}
        className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg font-semibold transition-colors"
      >
        <ArrowLeft className="h-5 w-5 inline mr-2" />
        Previous
      </button>
      <button
        onClick={onNext}
        className="flex-1 bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-semibold transition-colors"
      >
        Complete Setup
        <CheckCircle className="h-5 w-5 inline ml-2" />
      </button>
    </div>
  </div>
);

const steps: SetupStep[] = [
  {
    id: 'profile',
    title: 'Restaurant Profile',
    description: 'Tell us about your restaurant',
    icon: Store,
    component: RestaurantProfileStep
  },
  {
    id: 'location',
    title: 'Location Setup',
    description: 'Set your restaurant location',
    icon: MapPin,
    component: LocationSetupStep
  },
  {
    id: 'preferences',
    title: 'Preferences',
    description: 'Customize your experience',
    icon: Settings,
    component: PreferencesStep
  }
];

export default function SetupPage() {
  const { t } = useLanguage();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [setupData, setSetupData] = useState({});

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete setup
      // In real app, save data to API
      document.cookie = 'restaurant_setup_complete=true; path=/';
      router.push('/workspace');
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Restaurant Setup
          </h1>
          <p className="text-xl text-gray-600">
            Let's get your restaurant set up for success
          </p>
        </motion.div>

        {/* Progress Steps */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <motion.div
                  className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all ${
                    index <= currentStep
                      ? 'bg-orange-500 border-orange-500 text-white'
                      : 'bg-white border-gray-300 text-gray-400'
                  }`}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {index < currentStep ? (
                    <CheckCircle className="h-6 w-6" />
                  ) : (
                    <step.icon className="h-6 w-6" />
                  )}
                </motion.div>
                {index < steps.length - 1 && (
                  <div className={`w-24 h-1 mx-4 transition-all ${
                    index < currentStep ? 'bg-orange-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-4">
            {steps.map((step, index) => (
              <div key={step.id} className="text-center" style={{ width: index < steps.length - 1 ? 'calc(33.33% - 48px)' : '33.33%' }}>
                <h3 className={`font-semibold ${
                  index <= currentStep ? 'text-orange-500' : 'text-gray-400'
                }`}>
                  {step.title}
                </h3>
                <p className={`text-sm ${
                  index <= currentStep ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Current Step */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          className="max-w-2xl mx-auto bg-white rounded-2xl border border-gray-200 shadow-lg p-8"
        >
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-2xl mb-4">
              {React.createElement(steps[currentStep].icon, { className: "h-8 w-8 text-orange-500" })}
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              {steps[currentStep].title}
            </h2>
            <p className="text-gray-600">
              {steps[currentStep].description}
            </p>
          </div>

          <CurrentStepComponent
            onNext={handleNext}
            onPrevious={handlePrevious}
            data={setupData}
            setData={setSetupData}
          />
        </motion.div>
      </div>
    </div>
  );
}