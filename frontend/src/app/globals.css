@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');
@import "tailwindcss";

/* Custom animations for stable performance */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Enhanced Landing Page Styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
}

.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom gradient animations */
@keyframes gradient-x {
  0%, 100% {
    transform: translateX(0%);
  }
  50% {
    transform: translateX(100%);
  }
}

@keyframes gradient-y {
  0%, 100% {
    transform: translateY(0%);
  }
  50% {
    transform: translateY(100%);
  }
}

@keyframes gradient-xy {
  0%, 100% {
    transform: translate(0%, 0%);
  }
  25% {
    transform: translate(100%, 0%);
  }
  50% {
    transform: translate(100%, 100%);
  }
  75% {
    transform: translate(0%, 100%);
  }
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
}

.animate-gradient-y {
  animation: gradient-y 15s ease infinite;
}

.animate-gradient-xy {
  animation: gradient-xy 15s ease infinite;
}

/* Enhanced glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* DB Helvetica Font Face (fallback to system fonts if not available) */
@font-face {
  font-family: 'DB Helvetica';
  src: local('Helvetica Neue'), local('Helvetica'), local('Arial'), sans-serif;
  font-weight: normal;
  font-style: normal;
}

/* BiteBase Intelligence Style Guide - Corporate Identity 2024 */
:root {
  /* Brand Colors (Corporate Identity 2024) */
  --color-brand-primary: #74C365; /* Mantis Green */
  --color-brand-primary-hover: #68B359;
  --color-brand-primary-light: rgba(116, 195, 101, 0.1);
  --color-brand-accent: #E23D28; /* Chili Red */
  --color-brand-accent-hover: #D12E18;
  --color-brand-accent-light: rgba(226, 61, 40, 0.1);
  --color-brand-secondary: #F4C431; /* Saffron */
  --color-brand-secondary-light: rgba(244, 196, 49, 0.1);

  /* Legacy Brand Colors (Compatibility) */
  --color-legacy-orange: #FF6B35;
  --color-legacy-orange-hover: #E55A2B;

  /* Dark Theme Base Colors */
  --background: #0F172A;
  --foreground: #F8FAFC;
  --card: #1E293B;
  --card-foreground: #F8FAFC;
  --popover: #1E293B;
  --popover-foreground: #F8FAFC;
  --primary: #74C365;
  --primary-foreground: #FFFFFF;
  --secondary: #334155;
  --secondary-foreground: #F8FAFC;
  --muted: #334155;
  --muted-foreground: #94A3B8;
  --accent: #E23D28;
  --accent-foreground: #FFFFFF;
  --destructive: #E74C3C;
  --destructive-foreground: #FFFFFF;
  --border: #475569;
  --input: #1E293B;
  --ring: #74C365;
  --radius: 0.5rem;

  /* Chart Colors (Updated CI) */
  --chart-1: #74C365; /* Mantis Green - primary data series */
  --chart-2: #E23D28; /* Chili Red - secondary/contrast data */
  --chart-3: #F4C431; /* Saffron - tertiary/accent data */
  --chart-4: #2196F3; /* Info Blue - additional data series */
  --chart-5: #6C757D; /* Neutral - neutral data and baselines */
}

/* Custom gradient utilities */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: 'DB Helvetica', 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --radius: var(--radius);
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'DB Helvetica', 'Inter', system-ui, sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
  min-height: 100vh;
  background-image:
    radial-gradient(circle at 20% 50%, rgba(116, 195, 101, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(226, 61, 40, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(244, 196, 49, 0.02) 0%, transparent 50%);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-brand-primary-hover);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(116, 195, 101, 0.1);
}

/* Glow effects */
.glow-green {
  box-shadow: 0 0 20px rgba(116, 195, 101, 0.3);
}

.glow-red {
  box-shadow: 0 0 20px rgba(226, 61, 40, 0.3);
}

.glow-yellow {
  box-shadow: 0 0 20px rgba(244, 196, 49, 0.3);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #74C365 0%, #E23D28 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card styles */
.card-light {
  background: var(--card);
  border: 1px solid var(--border);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: var(--radius);
}

.card-light:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: var(--color-brand-primary);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, #74C365 0%, #68B359 100%);
  color: white;
  border: none;
  transition: all 0.2s ease;
  border-radius: var(--radius);
  font-family: 'JetBrains Mono', monospace;
  font-weight: 500;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #68B359 0%, #5FA854 100%);
  box-shadow: 0 0 20px rgba(116, 195, 101, 0.4);
}

.btn-accent {
  background: linear-gradient(135deg, #E23D28 0%, #D12E18 100%);
  color: white;
  border: none;
  transition: all 0.2s ease;
  border-radius: var(--radius);
  font-family: 'JetBrains Mono', monospace;
  font-weight: 500;
}

.btn-accent:hover {
  background: linear-gradient(135deg, #D12E18 0%, #C02A15 100%);
  box-shadow: 0 0 20px rgba(226, 61, 40, 0.4);
}

/* Status indicators */
.status-success { color: #74C365; background: rgba(116, 195, 101, 0.1); }
.status-warning { color: #F4C431; background: rgba(244, 196, 49, 0.1); }
.status-error { color: #E23D28; background: rgba(226, 61, 40, 0.1); }
.status-info { color: #2196F3; background: rgba(33, 150, 243, 0.1); }

/* Score indicators */
.score-excellent { color: #74C365; }
.score-good { color: #68B359; }
.score-fair { color: #F4C431; }
.score-poor { color: #E23D28; }

/* Map styles */
.leaflet-container {
  background: var(--muted) !important;
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.leaflet-popup-content-wrapper {
  background: var(--card) !important;
  color: var(--foreground) !important;
  border: 1px solid var(--border) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-tip {
  background: var(--card) !important;
  border: 1px solid var(--border) !important;
}

/* Typography Classes */
.font-primary {
  font-family: 'JetBrains Mono', monospace;
}

.font-secondary {
  font-family: 'DB Helvetica', 'Inter', system-ui, sans-serif;
}

/* Brand Color Utilities */
.text-brand-primary { color: var(--color-brand-primary); }
.text-brand-accent { color: var(--color-brand-accent); }
.text-brand-secondary { color: var(--color-brand-secondary); }
.bg-brand-primary { background-color: var(--color-brand-primary); }
.bg-brand-accent { background-color: var(--color-brand-accent); }
.bg-brand-secondary { background-color: var(--color-brand-secondary); }
.border-brand-primary { border-color: var(--color-brand-primary); }
