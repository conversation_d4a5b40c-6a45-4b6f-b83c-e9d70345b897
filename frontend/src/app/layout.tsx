'use client';

import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from 'sonner';
import EnhancedCopilotKit from '@/components/ai/EnhancedCopilotKit';

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="light">
      <head>
        <title>BiteBase Intelligence</title>
        <meta name="description" content="AI-Powered Business Intelligence Platform for Restaurant & Cafe Industry" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body
        className={`${inter.variable} font-secondary antialiased bg-background text-foreground`}
      >
        {/* Header */}
        <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-xl shadow-lg">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">B</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-bold text-gray-900">BiteBase</span>
                  <span className="text-sm text-orange-500">Intelligence</span>
                </div>
              </div>
              <nav className="hidden md:flex items-center space-x-8">
                <a href="#features" className="text-gray-700 hover:text-orange-500 transition-colors">Features</a>
                <a href="#pricing" className="text-gray-700 hover:text-orange-500 transition-colors">Pricing</a>
                <a href="#about" className="text-gray-700 hover:text-orange-500 transition-colors">About</a>
                <button className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all">
                  Get Started
                </button>
              </nav>
            </div>
          </div>
        </header>

        <main className="min-h-screen pt-20">
          {children}
        </main>

        {/* Enhanced CopilotKit Integration */}
        <EnhancedCopilotKit
          mode="floating"
          context="general"
          tools="all"
          enableVoice={true}
          enableStreaming={true}
          className="fixed bottom-6 right-6 z-50"
        />

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'white',
              color: 'black',
              border: '1px solid #e5e7eb'
            }
          }}
        />

        {/* Footer */}
        <footer className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
          <div className="max-w-7xl mx-auto px-6 py-12">
            <div className="grid md:grid-cols-4 gap-8">
              <div className="md:col-span-1">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                    <span className="text-white font-bold text-lg">B</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-xl font-bold text-white">BiteBase</span>
                    <span className="text-sm text-orange-400">Intelligence</span>
                  </div>
                </div>
                <p className="text-gray-300 text-sm mb-4">
                  AI-powered business intelligence platform for the restaurant industry.
                </p>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Solutions</h3>
                <ul className="space-y-2">
                  <li><a href="/analytics" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Analytics Dashboard</a></li>
                  <li><a href="/location-intelligence" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Location Intelligence</a></li>
                  <li><a href="/research-agent" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">AI Research Agent</a></li>
                  <li><a href="/multi-location" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Multi-Location</a></li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Company</h3>
                <ul className="space-y-2">
                  <li><a href="/about" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">About Us</a></li>
                  <li><a href="/careers" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Careers</a></li>
                  <li><a href="/blog" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Blog</a></li>
                  <li><a href="/contact" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Contact</a></li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Legal</h3>
                <ul className="space-y-2">
                  <li><a href="/privacy" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Privacy Policy</a></li>
                  <li><a href="/terms" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Terms of Service</a></li>
                  <li><a href="/cookies" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Cookie Policy</a></li>
                  <li><a href="/security" className="text-gray-300 hover:text-orange-400 text-sm transition-colors">Security</a></li>
                </ul>
              </div>
            </div>
            <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                © {new Date().getFullYear()} BiteBase Intelligence. All rights reserved.
              </p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="https://twitter.com/bitebase" className="text-gray-400 hover:text-orange-400 transition-colors">
                  <span className="sr-only">Twitter</span>
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="https://linkedin.com/company/bitebase" className="text-gray-400 hover:text-orange-400 transition-colors">
                  <span className="sr-only">LinkedIn</span>
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
}
