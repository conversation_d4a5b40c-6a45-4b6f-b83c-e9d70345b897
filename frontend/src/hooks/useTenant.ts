import { useState, useEffect, useContext, createContext } from 'react';

interface Tenant {
  id: string;
  name: string;
  domain: string;
  plan: 'starter' | 'professional' | 'enterprise';
  features: string[];
  settings: {
    branding: {
      logo: string;
      primaryColor: string;
      secondaryColor: string;
    };
    integrations: {
      pos: string[];
      analytics: string[];
      marketing: string[];
    };
    limits: {
      locations: number;
      users: number;
      apiCalls: number;
    };
  };
}

interface TenantContextType {
  tenant: Tenant | null;
  isLoading: boolean;
  updateTenant: (updates: Partial<Tenant>) => void;
  switchTenant: (tenantId: string) => Promise<void>;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function useTenant() {
  const context = useContext(TenantContext);
  if (!context) {
    // Mock implementation for development
    return {
      tenant: {
        id: 'tenant-1',
        name: 'Demo Restaurant Group',
        domain: 'demo.bitebase.app',
        plan: 'professional' as const,
        features: ['analytics', 'location-intelligence', 'ai-assistant', 'reports'],
        settings: {
          branding: {
            logo: '/logo.png',
            primaryColor: '#f97316',
            secondaryColor: '#ea580c'
          },
          integrations: {
            pos: ['square', 'toast'],
            analytics: ['google-analytics'],
            marketing: ['mailchimp']
          },
          limits: {
            locations: 10,
            users: 25,
            apiCalls: 10000
          }
        }
      },
      isLoading: false,
      updateTenant: () => {},
      switchTenant: async () => {}
    };
  }
  return context;
}
