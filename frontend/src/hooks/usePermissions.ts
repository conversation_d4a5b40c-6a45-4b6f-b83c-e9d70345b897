import { useAuth } from './useAuth';
import { useTenant } from './useTenant';

interface Permission {
  resource: string;
  action: string;
  scope?: 'own' | 'tenant' | 'global';
}

export function usePermissions() {
  const { user } = useAuth();
  const { tenant } = useTenant();

  const hasPermission = (resource: string, action: string, scope: 'own' | 'tenant' | 'global' = 'tenant'): boolean => {
    if (!user || !user.permissions) return false;

    // Admin users have all permissions
    if (user.role === 'admin') return true;

    // Check specific permission
    const permissionKey = `${resource}:${action}`;
    const hasSpecificPermission = user.permissions.includes(permissionKey);

    // Check wildcard permissions
    const hasResourceWildcard = user.permissions.includes(`${resource}:*`);
    const hasGlobalWildcard = user.permissions.includes('*:*');

    return hasSpecificPermission || hasResourceWildcard || hasGlobalWildcard;
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => {
      const [resource, action] = permission.split(':');
      return hasPermission(resource, action);
    });
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => {
      const [resource, action] = permission.split(':');
      return hasPermission(resource, action);
    });
  };

  const getPermissions = (): string[] => {
    return user?.permissions || [];
  };

  const canAccessFeature = (feature: string): boolean => {
    if (!tenant?.features) return false;
    return tenant.features.includes(feature);
  };

  const isWithinLimits = (resource: string, currentCount: number): boolean => {
    if (!tenant?.settings?.limits) return true;
    
    const limits = tenant.settings.limits;
    switch (resource) {
      case 'locations':
        return currentCount < limits.locations;
      case 'users':
        return currentCount < limits.users;
      case 'apiCalls':
        return currentCount < limits.apiCalls;
      default:
        return true;
    }
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getPermissions,
    canAccessFeature,
    isWithinLimits,
    userRole: user?.role || 'guest',
    tenantPlan: tenant?.plan || 'starter'
  };
}
