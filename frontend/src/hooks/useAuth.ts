import { useState, useEffect, useContext, createContext } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  tenantId: string;
  permissions: string[];
  preferences: {
    theme: 'light' | 'dark';
    language: 'en' | 'th';
    currency: string;
    dateFormat: string;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    // Mock implementation for development
    return {
      user: {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'Demo User',
        role: 'admin',
        tenantId: 'tenant-1',
        permissions: ['analytics:read', 'analytics:write', 'locations:analyze', 'reports:generate'],
        preferences: {
          theme: 'light' as const,
          language: 'en' as const,
          currency: 'USD',
          dateFormat: 'MM/DD/YYYY'
        }
      },
      isAuthenticated: true,
      isLoading: false,
      login: async () => {},
      logout: () => {},
      updateUser: () => {}
    };
  }
  return context;
}
