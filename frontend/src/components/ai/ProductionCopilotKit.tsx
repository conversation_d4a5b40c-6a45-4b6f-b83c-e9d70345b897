"use client";

/**
 * Enhanced Production-Ready CopilotKit Integration
 * Enterprise-grade AI assistant with advanced microservices integration,
 * real-time streaming, multi-tenant support, and comprehensive analytics
 */

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  CopilotKit,
  useCopilotAction,
  useCopilotReadable,
  useCoAgentStateRender,
  useCopilotChatSuggestions
} from '@copilotkit/react-core';
import {
  Co<PERSON>lotPopup,
  CopilotChat,
  CopilotSidebar,
  CopilotTextarea
} from '@copilotkit/react-ui';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Brain,
  MapPin,
  TrendingUp,
  Users,
  DollarSign,
  BarChart3,
  Mic,
  MicOff,
  Settings,
  Maximize2,
  Minimize2,
  RefreshCw,
  Download,
  Share2,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useTenant } from '@/hooks/useTenant';
import { usePermissions } from '@/hooks/usePermissions';
import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';

// Enhanced type definitions
type Integer = number;

interface EnhancedAIAssistantProps {
  mode?: 'popup' | 'sidebar' | 'embedded' | 'fullscreen' | 'floating';
  context?: 'dashboard' | 'map' | 'reports' | 'analytics' | 'general' | 'onboarding';
  tools?: 'all' | 'basic' | 'advanced' | 'custom' | string[];
  className?: string;
  theme?: 'light' | 'dark' | 'auto';
  enableVoice?: boolean;
  enableStreaming?: boolean;
  enableCollaboration?: boolean;
  maxTokens?: number;
  temperature?: number;
  model?: 'gpt-4-turbo' | 'gpt-4' | 'claude-3-sonnet' | 'claude-3-opus';
  onConversationUpdate?: (conversation: any) => void;
  onActionComplete?: (action: string, result: any) => void;
  customInstructions?: string;
}

interface AnalysisResult {
  success: boolean;
  data?: any;
  insights?: string[];
  recommendations?: string[];
  confidence?: number;
  sources?: string[];
  timestamp?: string;
}

interface StreamingState {
  isStreaming: boolean;
  currentMessage: string;
  progress: number;
  estimatedTime: number;
}

export function EnhancedProductionAIAssistant({
  mode = 'popup',
  context = 'general',
  tools = 'all',
  className,
  theme = 'auto',
  enableVoice = true,
  enableStreaming = true,
  enableCollaboration = false,
  maxTokens = 4096,
  temperature = 0.7,
  model = 'gpt-4-turbo',
  onConversationUpdate,
  onActionComplete,
  customInstructions
}: EnhancedAIAssistantProps) {
  const { user } = useAuth();
  const { tenant } = useTenant();
  const { hasPermission, canAccessFeature } = usePermissions();

  // Enhanced state management
  const [isLoading, setIsLoading] = useState(false);
  const [conversationHistory, setConversationHistory] = useState([]);
  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    currentMessage: '',
    progress: 0,
    estimatedTime: 0
  });
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTools, setActiveTools] = useState<string[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    responseTime: 0,
    tokensUsed: 0,
    apiCalls: 0,
    successRate: 100
  });

  // Refs for advanced features
  const conversationRef = useRef<any>(null);
  const voiceRecognitionRef = useRef<any>(null);
  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced dynamic instructions based on context, permissions, and tenant features
  const instructions = useMemo(() => {
    const baseInstructions = `You are BiteBase AI, an advanced restaurant intelligence assistant powered by cutting-edge AI technology.
    You specialize in location analysis, market research, competitor insights, predictive analytics, and comprehensive business intelligence.
    Always provide data-driven recommendations, actionable insights, and strategic guidance tailored to the restaurant industry.`;

    const contextInstructions = {
      dashboard: "Focus on dashboard insights, KPI analysis, business performance metrics, and real-time data interpretation. Help users understand trends and identify opportunities.",
      map: "Specialize in location analysis, demographic insights, geospatial intelligence, foot traffic patterns, and competitive landscape mapping.",
      reports: "Assist with comprehensive report generation, data analysis, business intelligence synthesis, and strategic recommendations.",
      analytics: "Provide advanced analytics, predictive modeling, trend analysis, forecasting, and deep statistical insights.",
      onboarding: "Guide new users through BiteBase features, explain capabilities, and help them get started with their restaurant intelligence journey.",
      general: "Assist with all aspects of restaurant business intelligence, strategic planning, and operational optimization."
    };

    const permissionContext = hasPermission('analytics', 'advanced')
      ? "You have access to advanced analytics, predictive modeling, and can provide detailed technical insights with confidence scores."
      : "Provide general insights based on available data while respecting user permission levels.";

    const tenantContext = tenant ? `
    Current tenant: ${tenant.name} (${tenant.plan} plan)
    Available features: ${tenant.features.join(', ')}
    Customize responses based on their plan capabilities and feature access.` : '';

    const customContext = customInstructions ? `\n\nAdditional instructions: ${customInstructions}` : '';

    return `${baseInstructions}\n\n${contextInstructions[context as keyof typeof contextInstructions]}\n\n${permissionContext}${tenantContext}${customContext}`;
  }, [context, hasPermission, tenant, customInstructions]);

  // Location Analysis Tool
  useCopilotAction({
    name: "analyzeLocation",
    description: "Analyze restaurant location potential with demographic and market data",
    parameters: [
      { name: "latitude", type: "number", description: "Latitude coordinate" },
      { name: "longitude", type: "number", description: "Longitude coordinate" },
      { name: "radius", type: "number", description: "Analysis radius in kilometers", default: 2 },
      { name: "analysisType", type: "string", description: "Type of analysis", enum: ["demographic", "competitive", "comprehensive"] }
    ],
    handler: async ({ latitude, longitude, radius = 2, analysisType = "comprehensive" }) => {
      if (!hasPermission('locations', 'analyze')) {
        throw new Error("Insufficient permissions for location analysis");
      }

      try {
        setIsLoading(true);
        const analysis = await apiClient.locations.analyze({
          latitude,
          longitude,
          radius_km: radius,
          analysis_type: analysisType,
          tenant_id: tenant.id
        });

        return {
          success: true,
          location: { latitude, longitude },
          radius_km: radius,
          analysis_type: analysisType,
          demographic_score: analysis.demographic_score,
          competition_level: analysis.competition_level,
          foot_traffic: analysis.foot_traffic,
          market_potential: analysis.market_potential,
          recommendations: analysis.recommendations,
          insights: analysis.insights
        };
      } catch (error) {
        toast.error("Location analysis failed");
        throw new Error(`Location analysis failed: ${error.message}`);
      } finally {
        setIsLoading(false);
      }
    }
  });

  // Market Research Tool
  useCopilotAction({
    name: "generateMarketReport",
    description: "Generate comprehensive market analysis report",
    parameters: [
      { name: "location", type: "object", description: "Location coordinates" },
      { name: "reportType", type: "string", description: "Type of report", enum: ["market_overview", "competitive_analysis", "demographic_study", "opportunity_assessment"] },
      { name: "timeframe", type: "string", description: "Analysis timeframe", enum: ["current", "6_months", "12_months", "24_months"] }
    ],
    renderAndWaitForResponse: ({ args, respond }) => (
      <MarketReportApproval
        location={args.location}
        reportType={args.reportType}
        timeframe={args.timeframe}
        onApprove={(config) => respond({ approved: true, config })}
        onReject={() => respond({ approved: false })}
        onCustomize={(config) => respond({ approved: true, config, customized: true })}
      />
    ),
    handler: async ({ location, reportType, timeframe, approved, config }) => {
      if (!approved) {
        return { success: false, message: "Report generation cancelled by user" };
      }

      if (!hasPermission('reports', 'generate')) {
        throw new Error("Insufficient permissions for report generation");
      }

      try {
        setIsLoading(true);
        const report = await apiClient.reports.generate({
          location,
          report_type: reportType,
          timeframe,
          config: config || {},
          tenant_id: tenant.id
        });

        return {
          success: true,
          report_id: report.id,
          report_type: reportType,
          location,
          timeframe,
          executive_summary: report.executive_summary,
          key_findings: report.key_findings,
          recommendations: report.recommendations,
          data_sources: report.data_sources,
          download_url: report.download_url
        };
      } catch (error) {
        toast.error("Report generation failed");
        throw new Error(`Report generation failed: ${error.message}`);
      } finally {
        setIsLoading(false);
      }
    }
  });

  // Competitor Analysis Tool
  useCopilotAction({
    name: "analyzeCompetitors",
    description: "Analyze competitors in a specific area",
    parameters: [
      { name: "location", type: "object", description: "Location coordinates" },
      { name: "radius", type: "number", description: "Search radius in kilometers", default: 3 },
      { name: "cuisineType", type: "string", description: "Filter by cuisine type", optional: true },
      { name: "priceRange", type: "string", description: "Filter by price range", enum: ["$", "$$", "$$$", "$$$$"], optional: true }
    ],
    render: ({ args, status }) => (
      <CompetitorAnalysisPreview
        location={args.location}
        radius={args.radius}
        cuisineType={args.cuisineType}
        priceRange={args.priceRange}
        isAnalyzing={status === "executing"}
      />
    ),
    handler: async ({ location, radius = 3, cuisineType, priceRange }) => {
      if (!hasPermission('competitors', 'analyze')) {
        throw new Error("Insufficient permissions for competitor analysis");
      }

      try {
        const competitors = await apiClient.competitors.search({
          latitude: location.latitude,
          longitude: location.longitude,
          radius_km: radius,
          cuisine_type: cuisineType,
          price_range: priceRange,
          tenant_id: tenant.id
        });

        return {
          success: true,
          location,
          search_radius: radius,
          filters: { cuisineType, priceRange },
          competitor_count: competitors.length,
          competitors: competitors.map(c => ({
            name: c.name,
            distance: c.distance,
            rating: c.rating,
            price_range: c.price_range,
            cuisine_type: c.cuisine_type,
            estimated_revenue: c.estimated_revenue,
            strengths: c.strengths,
            weaknesses: c.weaknesses
          })),
          market_analysis: {
            competition_density: competitors.length / (Math.PI * radius * radius),
            average_rating: competitors.reduce((sum, c) => sum + c.rating, 0) / competitors.length,
            price_distribution: this.calculatePriceDistribution(competitors),
            market_gaps: this.identifyMarketGaps(competitors)
          }
        };
      } catch (error) {
        toast.error("Competitor analysis failed");
        throw new Error(`Competitor analysis failed: ${error.message}`);
      }
    }
  });

  // Business Intelligence Tool
  useCopilotAction({
    name: "generateBusinessInsights",
    description: "Generate AI-powered business insights and recommendations",
    parameters: [
      { name: "dataType", type: "string", description: "Type of data to analyze", enum: ["sales", "performance", "trends", "opportunities"] },
      { name: "timeframe", type: "string", description: "Analysis timeframe", enum: ["week", "month", "quarter", "year"] },
      { name: "metrics", type: "array", description: "Specific metrics to focus on", optional: true }
    ],
    handler: async ({ dataType, timeframe, metrics = [] }) => {
      if (!hasPermission('analytics', 'read')) {
        throw new Error("Insufficient permissions for business analytics");
      }

      try {
        const insights = await apiClient.analytics.generateInsights({
          data_type: dataType,
          timeframe,
          metrics,
          tenant_id: tenant.id
        });

        return {
          success: true,
          data_type: dataType,
          timeframe,
          insights: insights.insights,
          recommendations: insights.recommendations,
          key_metrics: insights.key_metrics,
          trends: insights.trends,
          action_items: insights.action_items,
          confidence_score: insights.confidence_score
        };
      } catch (error) {
        toast.error("Business insights generation failed");
        throw new Error(`Business insights generation failed: ${error.message}`);
      }
    }
  });

  // Smart Dashboard Configuration
  useCopilotAction({
    name: "optimizeDashboard",
    description: "Suggest optimal dashboard layout and widgets based on user behavior and business goals",
    parameters: [
      { name: "currentLayout", type: "object", description: "Current dashboard configuration" },
      { name: "businessGoals", type: "array", description: "Primary business objectives" },
      { name: "userRole", type: "string", description: "User's role in the organization" }
    ],
    renderAndWaitForResponse: ({ args, respond }) => (
      <DashboardOptimizationPreview
        currentLayout={args.currentLayout}
        businessGoals={args.businessGoals}
        userRole={args.userRole}
        onApprove={(layout) => respond({ approved: true, layout })}
        onCustomize={(layout) => respond({ approved: true, layout, customized: true })}
        onReject={() => respond({ approved: false })}
      />
    ),
    handler: async ({ currentLayout, businessGoals, userRole, approved, layout }) => {
      if (!approved) {
        return { success: false, message: "Dashboard optimization cancelled" };
      }

      if (!hasPermission('dashboard', 'configure')) {
        throw new Error("Insufficient permissions for dashboard configuration");
      }

      try {
        const optimizedLayout = await apiClient.dashboard.optimize({
          current_layout: currentLayout,
          business_goals: businessGoals,
          user_role: userRole,
          suggested_layout: layout,
          tenant_id: tenant.id
        });

        return {
          success: true,
          optimized_layout: optimizedLayout,
          improvements: optimizedLayout.improvements,
          expected_benefits: optimizedLayout.expected_benefits,
          implementation_steps: optimizedLayout.implementation_steps
        };
      } catch (error) {
        toast.error("Dashboard optimization failed");
        throw new Error(`Dashboard optimization failed: ${error.message}`);
      }
    }
  });

  // Make current context readable to AI
  useCopilotReadable({
    description: "Current user context and tenant information",
    value: {
      user: {
        id: user?.id,
        role: user?.role,
        permissions: user?.permissions || []
      },
      tenant: {
        id: tenant?.id,
        name: tenant?.name,
        plan: tenant?.plan_type,
        features: tenant?.features || []
      },
      context,
      timestamp: new Date().toISOString()
    }
  });

  // Dynamic UI rendering based on AI state
  useCoAgentStateRender({
    name: "location_analyzer",
    render: ({ state }) => {
      if (state.analysis_type === 'demographic') {
        return <DemographicAnalysisDisplay data={state.demographic_data} />;
      } else if (state.analysis_type === 'competitive') {
        return <CompetitiveAnalysisDisplay data={state.competitive_data} />;
      } else if (state.analysis_type === 'comprehensive') {
        return <ComprehensiveAnalysisDisplay data={state.analysis_data} />;
      }
      return null;
    }
  });

  // Render appropriate UI based on mode
  const renderAIInterface = () => {
    const commonProps = {
      instructions,
      className: `ai-assistant ${className || ''}`,
      onInProgress: setIsLoading,
      showResponseButton: true
    };

    switch (mode) {
      case 'popup':
        return (
          <CopilotPopup
            {...commonProps}
            labels={{
              title: "BiteBase AI Assistant",
              initial: "How can I help you with your restaurant business today?",
              placeholder: "Ask me about locations, competitors, market analysis..."
            }}
            defaultOpen={false}
          />
        );
      
      case 'sidebar':
        return (
          <CopilotSidebar
            {...commonProps}
            defaultOpen={true}
            clickOutsideToClose={false}
          />
        );
      
      case 'embedded':
        return (
          <div className="h-96 border rounded-lg">
            <CopilotChat {...commonProps} />
          </div>
        );
      
      case 'fullscreen':
        return (
          <div className="fixed inset-0 z-50 bg-white">
            <CopilotChat {...commonProps} />
          </div>
        );
      
      default:
        return <CopilotPopup {...commonProps} />;
    }
  };

  return (
    <CopilotKit 
      runtimeUrl="/api/copilotkit"
      agent="restaurant_intelligence_agent"
      showDevConsole={process.env.NODE_ENV === 'development'}
    >
      {renderAIInterface()}
      
      {/* Loading overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>AI is analyzing your request...</span>
            </div>
          </div>
        </div>
      )}
    </CopilotKit>
  );
}

// Helper components for AI interactions
function MarketReportApproval({ location, reportType, timeframe, onApprove, onReject, onCustomize }) {
  return (
    <div className="p-4 border rounded-lg bg-blue-50">
      <h3 className="font-semibold mb-2">Market Report Configuration</h3>
      <div className="space-y-2 text-sm">
        <p><strong>Location:</strong> {location.latitude}, {location.longitude}</p>
        <p><strong>Report Type:</strong> {reportType}</p>
        <p><strong>Timeframe:</strong> {timeframe}</p>
      </div>
      <div className="flex space-x-2 mt-4">
        <button 
          onClick={() => onApprove({})}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Generate Report
        </button>
        <button 
          onClick={() => onCustomize({})}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          Customize
        </button>
        <button 
          onClick={onReject}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

function CompetitorAnalysisPreview({ location, radius, cuisineType, priceRange, isAnalyzing }) {
  return (
    <div className="p-4 border rounded-lg bg-green-50">
      <h3 className="font-semibold mb-2">Competitor Analysis</h3>
      <div className="space-y-1 text-sm">
        <p><strong>Location:</strong> {location.latitude}, {location.longitude}</p>
        <p><strong>Radius:</strong> {radius} km</p>
        {cuisineType && <p><strong>Cuisine:</strong> {cuisineType}</p>}
        {priceRange && <p><strong>Price Range:</strong> {priceRange}</p>}
      </div>
      {isAnalyzing && (
        <div className="mt-3 flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
          <span className="text-sm">Analyzing competitors...</span>
        </div>
      )}
    </div>
  );
}

function DashboardOptimizationPreview({ currentLayout, businessGoals, userRole, onApprove, onCustomize, onReject }) {
  return (
    <div className="p-4 border rounded-lg bg-purple-50">
      <h3 className="font-semibold mb-2">Dashboard Optimization</h3>
      <div className="space-y-2 text-sm">
        <p><strong>User Role:</strong> {userRole}</p>
        <p><strong>Business Goals:</strong> {businessGoals.join(', ')}</p>
        <p><strong>Current Widgets:</strong> {Object.keys(currentLayout).length}</p>
      </div>
      <div className="flex space-x-2 mt-4">
        <button 
          onClick={() => onApprove({})}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Apply Optimization
        </button>
        <button 
          onClick={() => onCustomize({})}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          Customize
        </button>
        <button 
          onClick={onReject}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

// Analysis display components
function DemographicAnalysisDisplay({ data }) {
  return (
    <div className="p-4 bg-blue-50 rounded-lg">
      <h4 className="font-semibold mb-2">Demographic Analysis</h4>
      {/* Demographic visualization */}
    </div>
  );
}

function CompetitiveAnalysisDisplay({ data }) {
  return (
    <div className="p-4 bg-red-50 rounded-lg">
      <h4 className="font-semibold mb-2">Competitive Analysis</h4>
      {/* Competitive visualization */}
    </div>
  );
}

function ComprehensiveAnalysisDisplay({ data }) {
  return (
    <div className="p-4 bg-green-50 rounded-lg">
      <h4 className="font-semibold mb-2">Comprehensive Analysis</h4>
      {/* Comprehensive visualization */}
    </div>
  );
}

export default ProductionAIAssistant;
