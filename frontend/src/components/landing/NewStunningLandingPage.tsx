'use client'

import React, { useState, useEffect } from 'react'
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import { <PERSON><PERSON>les, Zap, ArrowRight, Play, Brain, BarChart3, Users, Shield, Star, Check, ChevronDown, Menu, X, Rocket, Globe, Database } from 'lucide-react'
import Link from 'next/link'
import EnhancedCopilotKit from '@/components/ai/EnhancedCopilotKit'

// Stunning floating particles with glow effects
const StunningParticles = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(200)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full"
          style={{
            width: Math.random() * 10 + 4,
            height: Math.random() * 10 + 4,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            background: `radial-gradient(circle, hsl(${Math.random() * 360}, 90%, 70%), transparent)`,
            boxShadow: `0 0 ${Math.random() * 30 + 15}px hsl(${Math.random() * 360}, 90%, 70%)`,
            filter: 'blur(0.5px)',
          }}
          animate={{
            y: [0, -80, 0],
            x: [0, Math.random() * 40 - 20, 0],
            scale: [0.3, 2, 0.3],
            rotate: [0, 360, 720],
            opacity: [0.1, 1, 0.1],
          }}
          transition={{
            duration: Math.random() * 6 + 4,
            repeat: Infinity,
            delay: Math.random() * 4,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};

// Epic animated background with multiple layers
const EpicBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Primary cosmic gradient */}
      <motion.div
        className="absolute inset-0"
        animate={{
          background: [
            "radial-gradient(circle at 30% 40%, #667eea 0%, transparent 70%), radial-gradient(circle at 70% 60%, #764ba2 0%, transparent 70%), radial-gradient(circle at 50% 20%, #f093fb 0%, transparent 70%)",
            "radial-gradient(circle at 80% 20%, #f093fb 0%, transparent 70%), radial-gradient(circle at 20% 80%, #4facfe 0%, transparent 70%), radial-gradient(circle at 60% 50%, #43e97b 0%, transparent 70%)",
            "radial-gradient(circle at 10% 70%, #43e97b 0%, transparent 70%), radial-gradient(circle at 90% 30%, #fa709a 0%, transparent 70%), radial-gradient(circle at 40% 90%, #667eea 0%, transparent 70%)",
            "radial-gradient(circle at 30% 40%, #667eea 0%, transparent 70%), radial-gradient(circle at 70% 60%, #764ba2 0%, transparent 70%), radial-gradient(circle at 50% 20%, #f093fb 0%, transparent 70%)",
          ],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      
      {/* Sweeping light beams */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        style={{ transform: 'skewX(-15deg)' }}
        animate={{
          x: ["-150%", "150%"],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      
      {/* Pulsing energy orbs */}
      <motion.div
        className="absolute top-1/3 left-1/5 w-[500px] h-[500px] bg-gradient-radial from-purple-500/40 via-pink-500/20 to-transparent rounded-full blur-3xl"
        animate={{
          scale: [0.8, 1.4, 0.8],
          opacity: [0.3, 0.8, 0.3],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      
      <motion.div
        className="absolute bottom-1/3 right-1/5 w-[400px] h-[400px] bg-gradient-radial from-cyan-500/40 via-blue-500/20 to-transparent rounded-full blur-3xl"
        animate={{
          scale: [1.2, 0.6, 1.2],
          opacity: [0.4, 0.9, 0.4],
          rotate: [360, 180, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3,
        }}
      />
      
      {/* Floating geometric shapes */}
      <motion.div
        className="absolute top-1/4 right-1/3 w-20 h-20 border-2 border-white/30 rotate-45"
        animate={{
          y: [0, -50, 0],
          rotate: [45, 405, 45],
          opacity: [0.3, 0.8, 0.3],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      
      <motion.div
        className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-gradient-to-r from-pink-500/30 to-purple-500/30 rounded-full"
        animate={{
          x: [0, 30, 0],
          scale: [1, 1.5, 1],
          opacity: [0.4, 0.9, 0.4],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />
    </div>
  );
};

// Hero section with stunning effects
const StunningHeroSection = () => {
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0]);

  return (
    <motion.section
      style={{ y, opacity }}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900"
    >
      <EpicBackground />
      <StunningParticles />
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.2 }}
        >
          <motion.div
            className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/30 to-pink-500/30 border border-white/20 backdrop-blur-sm mb-8"
            whileHover={{ scale: 1.05 }}
            animate={{
              borderColor: [
                "rgba(255, 255, 255, 0.2)",
                "rgba(168, 85, 247, 0.5)",
                "rgba(236, 72, 153, 0.5)",
                "rgba(59, 130, 246, 0.5)",
                "rgba(255, 255, 255, 0.2)",
              ],
              boxShadow: [
                "0 0 20px rgba(168, 85, 247, 0.3)",
                "0 0 30px rgba(236, 72, 153, 0.4)",
                "0 0 25px rgba(59, 130, 246, 0.3)",
                "0 0 20px rgba(168, 85, 247, 0.3)",
              ],
            }}
            transition={{ duration: 4, repeat: Infinity }}
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles className="w-6 h-6 text-yellow-300 mr-3" />
            </motion.div>
            <span className="text-white text-lg font-semibold tracking-wide">AI-Powered Business Intelligence</span>
          </motion.div>

          <motion.h1
            className="text-6xl md:text-8xl font-bold leading-tight mb-8"
            initial={{ opacity: 0, y: 30, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 1.2, delay: 0.4, type: "spring", stiffness: 100 }}
          >
            <motion.span
              className="bg-gradient-to-r from-white via-cyan-200 to-blue-200 bg-clip-text text-transparent"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                backgroundSize: "200% 200%",
              }}
            >
              BiteBase
            </motion.span>
            <br />
            <motion.span
              className="bg-gradient-to-r from-purple-300 via-pink-300 to-cyan-300 bg-clip-text text-transparent"
              animate={{
                backgroundPosition: ["100% 50%", "0% 50%", "100% 50%"],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                backgroundSize: "200% 200%",
              }}
            >
              Intelligence
            </motion.span>
          </motion.h1>

          <motion.p
            className="text-2xl md:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
            style={{
              textShadow: "0 0 20px rgba(255, 255, 255, 0.3)",
            }}
          >
            Transform your restaurant business with{" "}
            <motion.span
              className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                backgroundSize: "200% 200%",
              }}
            >
              AI-powered intelligence
            </motion.span>
            , interactive market research, and real-time analytics that drive unprecedented success.
          </motion.p>
        </motion.div>

        <motion.div
          className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.8 }}
        >
          <Link href="/location-intelligence">
            <motion.button
              className="group relative px-10 py-5 bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 rounded-2xl text-white font-bold text-xl shadow-2xl overflow-hidden"
              whileHover={{ 
                scale: 1.05,
                rotateY: 5,
                rotateX: 5,
              }}
              whileTap={{ scale: 0.95 }}
              animate={{
                boxShadow: [
                  "0 0 30px rgba(168, 85, 247, 0.5)",
                  "0 0 50px rgba(236, 72, 153, 0.7)",
                  "0 0 40px rgba(59, 130, 246, 0.6)",
                  "0 0 30px rgba(168, 85, 247, 0.5)",
                ],
              }}
              transition={{
                boxShadow: { duration: 3, repeat: Infinity },
              }}
              style={{
                transformStyle: "preserve-3d",
              }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-purple-700 via-pink-700 to-blue-700"
                initial={{ x: "-100%" }}
                whileHover={{ x: "0%" }}
                transition={{ duration: 0.3 }}
              />
              <span className="relative z-10 flex items-center">
                Start Your Journey
                <motion.div
                  className="ml-3"
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <ArrowRight className="w-6 h-6" />
                </motion.div>
              </span>
            </motion.button>
          </Link>

          <motion.button
            className="px-10 py-5 border-2 border-white/30 rounded-2xl text-white font-bold text-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm flex items-center"
            whileHover={{ 
              scale: 1.05,
              borderColor: "rgba(255, 255, 255, 0.6)",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            }}
            whileTap={{ scale: 0.95 }}
          >
            <Play className="w-6 h-6 mr-3" />
            Watch Demo
          </motion.button>
        </motion.div>
      </div>
    </motion.section>
  );
};

// Main component
export default function NewStunningLandingPage() {
  return (
    <div className="min-h-screen bg-slate-900 text-white overflow-hidden">
      <StunningHeroSection />
      
      {/* Enhanced CopilotKit Integration */}
      <EnhancedCopilotKit
        mode="floating"
        context="general"
        tools="all"
        enableVoice={true}
        enableStreaming={true}
        className="fixed bottom-6 right-6 z-50"
      />
    </div>
  );
}
