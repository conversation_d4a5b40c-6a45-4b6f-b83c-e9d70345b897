'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform, useInView, AnimatePresence } from 'framer-motion';
import { ChevronRightIcon, MapPinIcon, ChartBarIcon, SparklesIcon, ArrowRightIcon, PlayIcon } from '@heroicons/react/24/outline';
import { Brain, Star, Users, TrendingUp, Shield, Zap, Globe, MessageCircle, CheckCircle, BarChart3, Crown, Rocket } from 'lucide-react';
import Link from 'next/link';
import EnhancedCopilotKit from '@/components/ai/EnhancedCopilotKit';

// Floating particles animation component
const FloatingParticles = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(100)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full"
          style={{
            width: Math.random() * 6 + 2,
            height: Math.random() * 6 + 2,
            background: `linear-gradient(45deg,
              hsl(${Math.random() * 360}, 70%, 60%),
              hsl(${Math.random() * 360}, 80%, 70%))`,
          }}
          initial={{
            x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1200),
            y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800),
            scale: 0,
            opacity: 0,
          }}
          animate={{
            x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1200),
            y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800),
            scale: [0, 1, 0.5, 1],
            opacity: [0, 0.8, 0.3, 0.6],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: Math.random() * 15 + 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
            delay: Math.random() * 5,
          }}
        />
      ))}
    </div>
  );
};

// Animated gradient background
const AnimatedBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <motion.div
        className="absolute inset-0"
        animate={{
          background: [
            "linear-gradient(45deg, #667eea 0%, #764ba2 100%)",
            "linear-gradient(45deg, #f093fb 0%, #f5576c 100%)",
            "linear-gradient(45deg, #4facfe 0%, #00f2fe 100%)",
            "linear-gradient(45deg, #43e97b 0%, #38f9d7 100%)",
            "linear-gradient(45deg, #fa709a 0%, #fee140 100%)",
            "linear-gradient(45deg, #667eea 0%, #764ba2 100%)",
          ],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-purple-400/30 via-pink-400/30 to-blue-400/30"
        animate={{
          x: ["-100%", "100%"],
          opacity: [0.2, 0.8, 0.2],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute inset-0 bg-gradient-to-l from-cyan-400/20 via-emerald-400/20 to-yellow-400/20"
        animate={{
          x: ["100%", "-100%"],
          opacity: [0.1, 0.6, 0.1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: 2,
        }}
      />
      <FloatingParticles />
    </div>
  );
};

// Hero section with stunning animations
const HeroSection = () => {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0]);

  return (
    <motion.section
      ref={ref}
      style={{ y, opacity }}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
    >
      <AnimatedBackground />

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="mb-8"
        >
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/50 backdrop-blur-sm mb-6"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
            animate={{
              borderColor: [
                "rgba(168, 85, 247, 0.5)",
                "rgba(236, 72, 153, 0.5)",
                "rgba(59, 130, 246, 0.5)",
                "rgba(168, 85, 247, 0.5)",
              ],
            }}
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            >
              <SparklesIcon className="w-5 h-5 text-yellow-400 mr-2" />
            </motion.div>
            <span className="text-white text-sm font-medium font-secondary">AI-Powered Business Intelligence</span>
          </motion.div>

          <motion.h1
            className="text-5xl md:text-7xl font-primary font-bold leading-tight"
            initial={{ opacity: 0, y: 30, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 1, delay: 0.4, type: "spring", stiffness: 100 }}
          >
            <motion.span
              className="bg-gradient-to-r from-white via-cyan-200 to-blue-200 bg-clip-text text-transparent"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                backgroundSize: "200% 200%",
              }}
            >
              BiteBase
            </motion.span>
            <br />
            <motion.span
              className="bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1,
              }}
              style={{
                backgroundSize: "200% 200%",
              }}
            >
              Intelligence
            </motion.span>
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed mt-6 font-secondary"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
          >
            <motion.span
              animate={{
                color: [
                  "rgb(229, 231, 235)",
                  "rgb(196, 181, 253)",
                  "rgb(147, 197, 253)",
                  "rgb(229, 231, 235)",
                ],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              Transform your restaurant business with AI-powered location intelligence,
              interactive market research, and real-time analytics that drive success.
            </motion.span>
          </motion.p>
        </motion.div>

        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.8 }}
        >
          <Link href="/location-intelligence">
            <motion.button
              className="group relative px-8 py-4 bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 rounded-xl text-white font-semibold text-lg shadow-2xl overflow-hidden font-secondary perspective-1000"
              whileHover={{
                scale: 1.1,
                rotateY: 5,
                rotateX: 5,
                boxShadow: "0 25px 50px rgba(0, 0, 0, 0.3)",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300 }}
              animate={{
                boxShadow: [
                  "0 0 20px rgba(6, 182, 212, 0.5)",
                  "0 0 40px rgba(168, 85, 247, 0.5)",
                  "0 0 20px rgba(236, 72, 153, 0.5)",
                  "0 0 20px rgba(6, 182, 212, 0.5)",
                ],
              }}
              style={{
                background: "linear-gradient(45deg, #06b6d4, #a855f7, #ec4899)",
                backgroundSize: "300% 300%",
              }}
            >
              <motion.div
                className="absolute inset-0"
                animate={{
                  background: [
                    "linear-gradient(45deg, #06b6d4, #a855f7, #ec4899)",
                    "linear-gradient(45deg, #ec4899, #06b6d4, #a855f7)",
                    "linear-gradient(45deg, #a855f7, #ec4899, #06b6d4)",
                    "linear-gradient(45deg, #06b6d4, #a855f7, #ec4899)",
                  ],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              <span className="relative z-10 flex items-center">
                Start Analysis
                <motion.div
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  <ArrowRightIcon className="w-5 h-5 ml-2" />
                </motion.div>
              </span>
            </motion.button>
          </Link>

          <motion.button
            className="px-8 py-4 border-2 border-white/30 rounded-xl text-white font-semibold text-lg backdrop-blur-sm flex items-center font-secondary relative overflow-hidden"
            whileHover={{
              scale: 1.05,
              borderColor: "rgba(255, 255, 255, 0.8)",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            }}
            whileTap={{ scale: 0.95 }}
            animate={{
              borderColor: [
                "rgba(255, 255, 255, 0.3)",
                "rgba(6, 182, 212, 0.6)",
                "rgba(168, 85, 247, 0.6)",
                "rgba(255, 255, 255, 0.3)",
              ],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-purple-500/20 to-pink-500/20"
              animate={{
                x: ["-100%", "100%"],
                opacity: [0, 0.5, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <PlayIcon className="w-5 h-5 mr-2" />
            </motion.div>
            Watch Demo
          </motion.button>
        </motion.div>

      </div>

      {/* Scroll indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-6 h-10 border-2 border-slate-400 rounded-full flex justify-center">
          <motion.div
            className="w-1 h-3 bg-gradient-to-b from-blue-400 to-purple-400 rounded-full mt-2"
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
      </motion.div>
    </motion.section>
  );
};

// Core features section
const CoreFeaturesSection = () => {
  const features = [
    {
      icon: Brain,
      title: "AI Marketing Research Agent",
      description: "AI-powered agent that conducts comprehensive market research, analyzes competitors, and provides actionable insights for your business.",
      gradient: "from-blue-500 to-cyan-500",
      delay: 0.1,
      href: "/research-agent"
    },
    {
      icon: MapPinIcon,
      title: "Interactive Location Intelligence",
      description: "Advanced location intelligence with real-time data visualization, competitor mapping, and demographic analysis for optimal site selection.",
      gradient: "from-purple-500 to-pink-500",
      delay: 0.2,
      href: "/location-intelligence"
    },
    {
      icon: BarChart3,
      title: "Real-Time Analytics Engine",
      description: "Dynamic dashboards with live data processing, predictive analytics, and automated insights that adapt to market changes.",
      gradient: "from-green-500 to-emerald-500",
      delay: 0.3,
      href: "/analytics"
    }
  ];

  return (
    <section className="relative py-32 bg-gradient-to-b from-slate-900 via-purple-900/50 to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center space-x-2 bg-purple-500/20 text-purple-300 px-6 py-3 rounded-full font-semibold mb-8 border border-purple-400/30 font-secondary backdrop-blur-sm"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            animate={{
              borderColor: [
                "rgba(168, 85, 247, 0.3)",
                "rgba(236, 72, 153, 0.3)",
                "rgba(59, 130, 246, 0.3)",
                "rgba(168, 85, 247, 0.3)",
              ],
            }}
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            >
              <Zap className="w-5 h-5" />
            </motion.div>
            <span>Core Features</span>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-6xl font-primary font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-6"
            animate={{
              backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            style={{
              backgroundSize: "200% 200%",
            }}
          >
            Everything You Need to Succeed
          </motion.h2>
          <motion.p
            className="text-xl text-gray-300 max-w-3xl mx-auto font-secondary"
            animate={{
              color: [
                "rgb(209, 213, 219)",
                "rgb(196, 181, 253)",
                "rgb(147, 197, 253)",
                "rgb(209, 213, 219)",
              ],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            Powerful AI-driven tools designed for business intelligence
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="group relative"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: feature.delay }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
            >
              <Link href={feature.href}>
                <div className="relative p-8 bg-slate-800/50 backdrop-blur-sm rounded-3xl border border-slate-700 hover:border-purple-400/50 overflow-hidden cursor-pointer h-full shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 group-hover:scale-105">
                  {/* Animated background gradient */}
                  <motion.div
                    className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-500`}
                  />

                  {/* Icon */}
                  <motion.div
                    className={`inline-flex p-4 rounded-xl bg-gradient-to-br ${feature.gradient} mb-6`}
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.8 }}
                  >
                    <feature.icon className="w-8 h-8 text-white" />
                  </motion.div>

                  {/* Content */}
                  <h3 className="text-xl font-primary font-bold text-white mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-pink-400 group-hover:bg-clip-text transition-all duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-slate-300 leading-relaxed mb-6 font-secondary">
                    {feature.description}
                  </p>



                  {/* Learn more link */}
                  <motion.div
                    className="flex items-center text-purple-400 font-medium group-hover:text-pink-400 transition-colors duration-300 mt-4 font-secondary"
                    whileHover={{ x: 5 }}
                  >
                    Learn more
                    <ArrowRightIcon className="w-4 h-4 ml-1" />
                  </motion.div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Interactive demo section
const InteractiveDemoSection = () => {
  const [activeTab, setActiveTab] = useState(0);
  const tabs = [
    { name: "Location Intelligence", preview: "🗺️", description: "Interactive Map Analysis" },
    { name: "Market Research", preview: "🤖", description: "AI-Powered Insights" },
    { name: "Real-Time Analytics", preview: "📊", description: "Live Dashboard" }
  ];

  return (
    <section className="relative py-32 bg-gradient-to-b from-slate-900 via-blue-900/30 to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-6xl font-primary font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent mb-6">
            See It In Action
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto font-secondary">
            Experience the power of AI-driven restaurant intelligence
          </p>
        </motion.div>

        {/* Tab navigation */}
        <div className="flex justify-center mb-12">
          <div className="flex bg-card/50 rounded-xl p-2 backdrop-blur-sm border border-border">
            {tabs.map((tab, index) => (
              <motion.button
                key={index}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 font-secondary ${
                  activeTab === index
                    ? 'bg-gradient-to-r from-bitebase-primary to-bitebase-accent text-white shadow-lg'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
                onClick={() => setActiveTab(index)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {tab.name}
              </motion.button>
            ))}
          </div>
        </div>

        {/* Demo preview */}
        <motion.div
          className="relative max-w-5xl mx-auto"
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative bg-gradient-to-br from-card to-muted/30 rounded-2xl border border-border backdrop-blur-sm p-8 min-h-[400px] flex items-center justify-center shadow-lg">
            <div className="text-center">
              <motion.div
                className="text-8xl mb-6"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                {tabs[activeTab].preview}
              </motion.div>
              <h3 className="text-3xl font-primary font-bold text-foreground mb-4">{tabs[activeTab].name}</h3>
              <p className="text-muted-foreground text-lg mb-8 font-secondary">{tabs[activeTab].description}</p>
              <Link href={activeTab === 0 ? "/location-intelligence" : activeTab === 1 ? "/research-agent" : "/analytics"}>
                <motion.button
                  className="px-8 py-3 bg-gradient-to-r from-bitebase-primary to-bitebase-accent rounded-xl text-white font-semibold shadow-lg font-secondary"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Try Interactive Demo
                </motion.button>
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

// Stats section with animated counters
const StatsSection = () => {
  const stats = [
    { number: "99.9%", label: "Uptime Guarantee", suffix: "" },
    { number: "2", label: "Second Load Time", suffix: "s" },
    { number: "500", label: "Data Refresh", suffix: "ms" },
    { number: "24/7", label: "AI Monitoring", suffix: "" }
  ];

  return (
    <section className="relative py-20 bg-gradient-to-r from-purple-900/50 to-pink-900/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="text-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="text-4xl md:text-5xl font-primary font-bold bg-gradient-to-r from-bitebase-primary to-bitebase-accent bg-clip-text text-transparent mb-2"
                whileHover={{ scale: 1.1 }}
              >
                {stat.number}{stat.suffix}
              </motion.div>
              <p className="text-muted-foreground font-medium font-secondary">{stat.label}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Testimonials section
const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Restaurant Owner",
      company: "Golden Dragon Bistro",
      content: "BiteBase Intelligence helped us identify the perfect location for our second restaurant. The AI insights were spot-on, and we saw a 40% increase in foot traffic within the first month.",
      rating: 5,
      avatar: "👩‍🍳"
    },
    {
      name: "Marcus Rodriguez",
      role: "Operations Manager",
      company: "Taco Libre Chain",
      content: "The real-time analytics dashboard transformed how we make decisions. We can now predict busy periods and optimize staffing, resulting in 25% cost savings.",
      rating: 5,
      avatar: "👨‍💼"
    },
    {
      name: "Emily Watson",
      role: "Franchise Director",
      company: "Burger Palace",
      content: "The location intelligence feature is incredible. It helped us avoid costly mistakes and choose locations that consistently outperform our projections.",
      rating: 5,
      avatar: "👩‍💻"
    }
  ];

  return (
    <section className="relative py-32 bg-gradient-to-b from-slate-900 via-purple-900/30 to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center space-x-2 bg-purple-500/20 text-purple-300 px-6 py-3 rounded-full font-semibold mb-8 border border-purple-400/30 backdrop-blur-sm"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <span className="text-xl">⭐</span>
            <span>Customer Success Stories</span>
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-6">
            Trusted by Restaurant Leaders
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            See how restaurant owners and operators are transforming their businesses with BiteBase Intelligence
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl p-8 shadow-xl border border-orange-100 relative overflow-hidden"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -5, scale: 1.02 }}
            >
              {/* Rating stars */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: i * 0.1 }}
                  >
                    <Star className="w-5 h-5 text-yellow-400 fill-current" />
                  </motion.div>
                ))}
              </div>

              {/* Content */}
              <p className="text-slate-700 mb-6 leading-relaxed text-lg">
                "{testimonial.content}"
              </p>

              {/* Author */}
              <div className="flex items-center">
                <motion.div
                  className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-2xl mr-4"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  {testimonial.avatar}
                </motion.div>
                <div>
                  <div className="font-semibold text-slate-900">{testimonial.name}</div>
                  <div className="text-sm text-slate-600">{testimonial.role}</div>
                  <div className="text-sm text-orange-600 font-semibold">{testimonial.company}</div>
                </div>
              </div>

              {/* Decorative gradient */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-bl-full" />

              {/* Food emoji decoration */}
              <div className="absolute top-4 right-4 text-2xl opacity-20">
                {index === 0 ? '🍽️' : index === 1 ? '🌮' : '🍔'}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Pricing section
const PricingSection = () => {
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: "Starter",
      description: "Perfect for single restaurant owners",
      icon: Rocket,
      price: { monthly: 49, annual: 39 },
      originalPrice: { monthly: 69, annual: 55 },
      popular: false,
      color: 'from-blue-500 to-cyan-500',
      features: [
        "Real-time Analytics Dashboard",
        "Basic Location Intelligence",
        "Up to 1,000 orders/month",
        "Email Support",
        "Mobile App Access",
        "Basic Reporting",
        "Data Export"
      ],
      limitations: [
        "Limited to 1 location",
        "Basic AI insights only"
      ]
    },
    {
      name: "Professional",
      description: "Ideal for growing restaurant chains",
      icon: Crown,
      price: { monthly: 149, annual: 119 },
      originalPrice: { monthly: 199, annual: 159 },
      popular: true,
      color: 'from-orange-500 to-red-500',
      features: [
        "Everything in Starter",
        "Advanced AI Insights",
        "Multi-location Management",
        "Up to 10,000 orders/month",
        "Priority Support",
        "Custom Dashboards",
        "Advanced Analytics",
        "API Access",
        "Predictive Analytics",
        "Competitor Analysis"
      ],
      limitations: [
        "Up to 5 locations"
      ]
    },
    {
      name: "Enterprise",
      description: "For large restaurant enterprises",
      icon: Globe,
      price: { monthly: 399, annual: 319 },
      originalPrice: { monthly: 499, annual: 399 },
      popular: false,
      color: 'from-purple-500 to-pink-500',
      features: [
        "Everything in Professional",
        "Unlimited locations",
        "Unlimited orders",
        "24/7 Phone Support",
        "Custom Integrations",
        "White-label Options",
        "Advanced Security",
        "Dedicated Account Manager",
        "Custom Training",
        "SLA Guarantee"
      ],
      limitations: []
    }
  ];

  return (
    <section className="relative py-32 bg-gradient-to-b from-slate-900 via-indigo-900/30 to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center space-x-2 bg-gradient-to-r from-orange-100 to-red-100 text-orange-600 px-6 py-3 rounded-full font-semibold mb-8 border border-orange-200"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.05 }}
          >
            <SparklesIcon className="h-5 w-5" />
            <span>🔥 Limited Time: 30% Off All Plans</span>
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-6">
            Choose Your Plan
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto mb-12">
            Start with a plan that fits your needs. Upgrade anytime as your business grows.
          </p>

          {/* Billing toggle */}
          <div className="flex items-center justify-center space-x-4 mb-12">
            <span className={`font-medium ${!isAnnual ? 'text-slate-900' : 'text-slate-500'}`}>
              Monthly
            </span>
            <motion.button
              className={`relative w-14 h-7 rounded-full transition-colors duration-300 ${
                isAnnual ? 'bg-gradient-to-r from-orange-500 to-red-500' : 'bg-slate-300'
              }`}
              onClick={() => setIsAnnual(!isAnnual)}
              whileTap={{ scale: 0.95 }}
              whileHover={{ scale: 1.05 }}
            >
              <motion.div
                className="absolute top-1 w-5 h-5 bg-white rounded-full shadow-md"
                animate={{ x: isAnnual ? 32 : 4 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              />
            </motion.button>
            <span className={`font-medium ${isAnnual ? 'text-slate-900' : 'text-slate-500'}`}>
              Annual
            </span>
            {isAnnual && (
              <motion.span
                className="bg-gradient-to-r from-green-100 to-emerald-100 text-green-600 px-3 py-1 rounded-full text-sm font-bold border border-green-200"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                whileHover={{ scale: 1.05 }}
              >
                💰 Save 20%
              </motion.span>
            )}
          </div>
        </motion.div>

        {/* Pricing cards */}
        <div className="grid lg:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              className={`relative bg-white rounded-3xl shadow-2xl border-2 overflow-hidden ${
                plan.popular ? 'border-orange-500 scale-105' : 'border-gray-200'
              }`}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: plan.popular ? 1.05 : 1.02 }}
            >
              {/* Popular badge */}
              {plan.popular && (
                <motion.div
                  className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                    Most Popular
                  </div>
                </motion.div>
              )}

              <div className="p-8">
                {/* Plan header */}
                <div className="text-center mb-8">
                  <div className={`inline-flex p-3 rounded-xl bg-gradient-to-r ${plan.color} mb-4`}>
                    <plan.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 font-secondary">{plan.description}</p>
                </div>

                {/* Pricing */}
                <div className="text-center mb-8">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <span className="text-gray-400 line-through text-lg">
                      ${isAnnual ? plan.originalPrice.annual : plan.originalPrice.monthly}
                    </span>
                    <div className="bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-bold">
                      30% OFF
                    </div>
                  </div>
                  <div className="flex items-baseline justify-center">
                    <span className="text-5xl font-black text-gray-900">
                      ${isAnnual ? plan.price.annual : plan.price.monthly}
                    </span>
                    <span className="text-gray-600 ml-2">/month</span>
                  </div>
                  {isAnnual && (
                    <p className="text-sm text-gray-500 mt-1">
                      Billed annually (${(isAnnual ? plan.price.annual : plan.price.monthly) * 12})
                    </p>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <motion.div
                      key={featureIndex}
                      className="flex items-center space-x-3"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: featureIndex * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700 font-secondary">{feature}</span>
                    </motion.div>
                  ))}

                  {plan.limitations.map((limitation, limitIndex) => (
                    <motion.div
                      key={limitIndex}
                      className="flex items-center space-x-3 opacity-60"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 0.6, x: 0 }}
                      transition={{ duration: 0.5, delay: (plan.features.length + limitIndex) * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <div className="h-5 w-5 border-2 border-gray-300 rounded-full flex-shrink-0" />
                      <span className="text-gray-500 font-secondary">{limitation}</span>
                    </motion.div>
                  ))}
                </div>

                {/* CTA button */}
                <motion.button
                  className={`w-full py-4 rounded-xl font-bold text-lg transition-all duration-300 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {plan.popular ? 'Start Free Trial' : 'Get Started'}
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Enterprise contact */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="text-gray-600 font-secondary mb-4">
            Need a custom solution for your enterprise?
          </p>
          <Link href="/contact">
            <motion.button
              className="px-8 py-3 border-2 border-bitebase-primary text-bitebase-primary rounded-xl font-semibold hover:bg-bitebase-primary hover:text-white transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Contact Sales
            </motion.button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

// FAQ section
const FAQSection = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const faqs = [
    {
      question: "How does BiteBase Intelligence help with location selection?",
      answer: "Our AI analyzes demographic data, foot traffic patterns, competitor density, and local market trends to identify optimal locations for your restaurant. We provide detailed reports with risk assessments and revenue projections."
    },
    {
      question: "What kind of data sources does the platform use?",
      answer: "We integrate data from multiple sources including census data, social media analytics, local business directories, traffic patterns, real estate data, and proprietary market research to provide comprehensive insights."
    },
    {
      question: "Can I integrate BiteBase with my existing POS system?",
      answer: "Yes! We support integrations with major POS systems including Square, Toast, Clover, and many others. Our API allows for seamless data synchronization and real-time analytics."
    },
    {
      question: "How accurate are the AI predictions?",
      answer: "Our AI models have been trained on millions of data points and consistently achieve 85-95% accuracy in revenue predictions and market analysis. We continuously improve our models based on real-world outcomes."
    },
    {
      question: "Is there a free trial available?",
      answer: "Yes! We offer a 14-day free trial with full access to all features. No credit card required to start. You can explore all our tools and see the value before committing to a plan."
    },
    {
      question: "What support do you provide?",
      answer: "We provide comprehensive support including onboarding assistance, training materials, email support for all plans, and phone support for Professional and Enterprise plans. Enterprise customers get a dedicated account manager."
    }
  ];

  return (
    <section className="relative py-32 bg-gradient-to-b from-slate-900 via-cyan-900/30 to-slate-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center space-x-2 bg-orange-100 text-orange-600 px-6 py-3 rounded-full font-semibold mb-8"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <span className="text-xl">❓</span>
            <span>Frequently Asked Questions</span>
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-6">
            Got Questions? We've Got Answers
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Get answers to common questions about BiteBase Intelligence
          </p>
        </motion.div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl border border-orange-100 overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -2 }}
            >
              <motion.button
                className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-orange-50 transition-colors"
                onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                whileHover={{ backgroundColor: "#fff7ed" }}
              >
                <span className="font-semibold text-slate-900 text-lg">
                  {faq.question}
                </span>
                <motion.div
                  animate={{ rotate: openFAQ === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChevronRightIcon className="w-6 h-6 text-orange-500 transform rotate-90" />
                </motion.div>
              </motion.button>

              <AnimatePresence>
                {openFAQ === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden bg-gradient-to-r from-orange-50 to-red-50"
                  >
                    <div className="px-8 pb-6">
                      <p className="text-slate-700 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Enhanced features section
const EnhancedFeaturesSection = () => {
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Market Research",
      description: "Get comprehensive market analysis with competitor insights, customer behavior patterns, and growth opportunities.",
      benefits: ["Competitor Analysis", "Market Trends", "Customer Insights", "Growth Opportunities"],
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: MapPinIcon,
      title: "Advanced Location Intelligence",
      description: "Make data-driven location decisions with demographic analysis, foot traffic data, and site scoring.",
      benefits: ["Demographic Analysis", "Foot Traffic Data", "Site Scoring", "Risk Assessment"],
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: BarChart3,
      title: "Real-Time Analytics Dashboard",
      description: "Monitor your business performance with live dashboards, custom reports, and predictive analytics.",
      benefits: ["Live Dashboards", "Custom Reports", "Predictive Analytics", "Performance Metrics"],
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Users,
      title: "Multi-Location Management",
      description: "Manage multiple restaurant locations from a single dashboard with centralized reporting and insights.",
      benefits: ["Centralized Dashboard", "Cross-Location Analytics", "Performance Comparison", "Unified Reporting"],
      color: "from-orange-500 to-red-500"
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level security with data encryption, compliance certifications, and secure API access.",
      benefits: ["Data Encryption", "SOC 2 Compliance", "Secure APIs", "Access Controls"],
      color: "from-gray-600 to-gray-800"
    },
    {
      icon: Zap,
      title: "Automated Insights",
      description: "Receive automated alerts and recommendations based on your business data and market changes.",
      benefits: ["Smart Alerts", "Automated Reports", "Trend Detection", "Action Recommendations"],
      color: "from-yellow-500 to-orange-500"
    }
  ];

  return (
    <section className="relative py-32 bg-gradient-to-b from-slate-900 via-emerald-900/30 to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-6xl font-primary font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent mb-6">
            Powerful Features for Restaurant Success
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto font-secondary">
            Everything you need to make data-driven decisions and grow your restaurant business
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 relative overflow-hidden group"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              {/* Icon */}
              <div className={`inline-flex p-4 rounded-xl bg-gradient-to-r ${feature.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className="h-8 w-8 text-white" />
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 mb-4 font-primary">
                {feature.title}
              </h3>
              <p className="text-gray-600 mb-6 font-secondary leading-relaxed">
                {feature.description}
              </p>

              {/* Benefits */}
              <div className="space-y-2">
                {feature.benefits.map((benefit, benefitIndex) => (
                  <motion.div
                    key={benefitIndex}
                    className="flex items-center space-x-2"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: benefitIndex * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700 font-secondary">{benefit}</span>
                  </motion.div>
                ))}
              </div>

              {/* Decorative gradient */}
              <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${feature.color} opacity-10 rounded-bl-full group-hover:opacity-20 transition-opacity duration-300`} />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// CTA section with vibrant food theme
const CTASection = () => {
  return (
    <section className="relative py-32 bg-gradient-to-br from-purple-900 via-pink-900 to-indigo-900 overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute top-32 right-20 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-1/3 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>

        {/* Food emoji decorations */}
        <div className="absolute top-20 right-10 text-6xl opacity-10">🍕</div>
        <div className="absolute bottom-32 left-20 text-4xl opacity-10">🍔</div>
        <div className="absolute top-1/2 left-10 text-5xl opacity-10">🌮</div>
        <div className="absolute bottom-20 right-1/4 text-4xl opacity-10">🍟</div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-full font-semibold mb-8"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <span className="text-xl">🚀</span>
            <span>Ready to Get Started?</span>
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Transform Your Restaurant Business Today
          </h2>
          <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto">
            Join thousands of restaurant owners using AI-powered insights to drive growth and success.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/location-intelligence">
              <motion.button
                className="px-10 py-4 bg-white text-orange-600 rounded-xl font-bold text-lg shadow-2xl hover:bg-orange-50 transition-colors"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                🎯 Start Free Trial
              </motion.button>
            </Link>
            <motion.button
              className="px-10 py-4 border-2 border-white/50 rounded-xl text-white font-bold text-lg hover:border-white hover:bg-white/10 transition-all backdrop-blur-sm"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              📞 Schedule Demo
            </motion.button>
          </div>

          {/* Trust indicators */}
          <motion.div
            className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-white/80"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="text-center">
              <div className="text-2xl font-bold">1000+</div>
              <div className="text-sm">Happy Customers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">99.9%</div>
              <div className="text-sm">Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">24/7</div>
              <div className="text-sm">Support</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">14 Days</div>
              <div className="text-sm">Free Trial</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

// Main stunning landing page component
export default function StunningLandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 text-white overflow-hidden relative">
      {/* Navigation */}
      <motion.nav
        className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-slate-200"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.05 }}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">🍽️</span>
              </div>
              <div className="flex flex-col">
                <div className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                  BiteBase
                </div>
                <div className="text-xs text-slate-600">Intelligence</div>
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-8">
              <div className="flex items-center gap-6">
                {[
                  { href: "#features", label: "Features" },
                  { href: "#analytics", label: "Analytics" },
                  { href: "#pricing", label: "Pricing" },
                  { href: "#contact", label: "Contact" }
                ].map((item, index) => (
                  <motion.a
                    key={item.href}
                    href={item.href}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.3 }}
                    className="relative text-slate-600 hover:text-orange-600 transition-colors duration-300 group font-medium"
                  >
                    {item.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full"></span>
                  </motion.a>
                ))}
              </div>

              <Link href="/dashboard">
                <motion.button
                  className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-6 py-2 rounded-lg font-semibold transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Get Started
                </motion.button>
              </Link>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Main Content */}
      <HeroSection />
      <CoreFeaturesSection />
      <EnhancedFeaturesSection />
      <InteractiveDemoSection />
      <StatsSection />
      <TestimonialsSection />
      <PricingSection />
      <FAQSection />
      <CTASection />

      {/* Enhanced CopilotKit Integration */}
      <EnhancedCopilotKit
        mode="floating"
        context="general"
        tools="all"
        enableVoice={true}
        enableStreaming={true}
        className="fixed bottom-6 right-6 z-50"
      />
    </div>
  );
}
