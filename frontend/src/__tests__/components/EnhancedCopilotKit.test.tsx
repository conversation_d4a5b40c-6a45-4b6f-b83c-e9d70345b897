import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { EnhancedCopilotKit } from '../../components/ai/EnhancedCopilotKit';
import { useAuth } from '../../hooks/useAuth';
import { useTenant } from '../../hooks/useTenant';
import { usePermissions } from '../../hooks/usePermissions';

// Mock hooks
jest.mock('../../hooks/useAuth');
jest.mock('../../hooks/useTenant');
jest.mock('../../hooks/usePermissions');
jest.mock('@copilotkit/react-core');
jest.mock('@copilotkit/react-ui');

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseTenant = useTenant as jest.MockedFunction<typeof useTenant>;
const mockUsePermissions = usePermissions as jest.MockedFunction<typeof usePermissions>;

describe('EnhancedCopilotKit', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    // Mock default return values
    mockUseAuth.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'admin',
        tenantId: 'tenant-1',
        permissions: ['read', 'write'],
        preferences: {
          theme: 'light',
          language: 'en',
          timezone: 'UTC',
        },
      },
      isAuthenticated: true,
      login: jest.fn(),
      logout: jest.fn(),
      refreshToken: jest.fn(),
    });

    mockUseTenant.mockReturnValue({
      tenant: {
        id: 'tenant-1',
        name: 'Test Restaurant',
        plan: 'premium',
        features: ['analytics', 'ai_assistant'],
        branding: {
          primaryColor: '#007bff',
          logo: '/logo.png',
        },
        integrations: {
          pos: 'square',
          payments: 'stripe',
        },
        limits: {
          users: 50,
          locations: 10,
          apiCalls: 10000,
        },
      },
      switchTenant: jest.fn(),
      updateTenant: jest.fn(),
    });

    mockUsePermissions.mockReturnValue({
      permissions: ['read', 'write', 'admin'],
      hasPermission: jest.fn().mockReturnValue(true),
      hasAnyPermission: jest.fn().mockReturnValue(true),
      hasAllPermissions: jest.fn().mockReturnValue(true),
      canAccess: jest.fn().mockReturnValue(true),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  describe('Component Rendering', () => {
    it('renders without crashing', () => {
      renderWithProviders(<EnhancedCopilotKit />);
      expect(screen.getByTestId('enhanced-copilotkit')).toBeInTheDocument();
    });

    it('displays user information correctly', () => {
      renderWithProviders(<EnhancedCopilotKit />);
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('Test Restaurant')).toBeInTheDocument();
    });

    it('shows performance metrics', () => {
      renderWithProviders(<EnhancedCopilotKit />);
      expect(screen.getByTestId('performance-metrics')).toBeInTheDocument();
    });
  });

  describe('Voice Recognition', () => {
    it('enables voice recognition when supported', async () => {
      // Mock speech recognition
      const mockSpeechRecognition = {
        start: jest.fn(),
        stop: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      };
      
      (global as any).SpeechRecognition = jest.fn(() => mockSpeechRecognition);
      (global as any).webkitSpeechRecognition = jest.fn(() => mockSpeechRecognition);

      renderWithProviders(<EnhancedCopilotKit />);
      
      const voiceButton = screen.getByTestId('voice-recognition-button');
      expect(voiceButton).toBeInTheDocument();
      
      fireEvent.click(voiceButton);
      expect(mockSpeechRecognition.start).toHaveBeenCalled();
    });

    it('handles voice recognition errors gracefully', async () => {
      const mockSpeechRecognition = {
        start: jest.fn().mockImplementation(() => {
          throw new Error('Speech recognition not available');
        }),
        stop: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      };
      
      (global as any).SpeechRecognition = jest.fn(() => mockSpeechRecognition);

      renderWithProviders(<EnhancedCopilotKit />);
      
      const voiceButton = screen.getByTestId('voice-recognition-button');
      fireEvent.click(voiceButton);
      
      await waitFor(() => {
        expect(screen.getByText(/voice recognition not available/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance Monitoring', () => {
    it('tracks response times', async () => {
      renderWithProviders(<EnhancedCopilotKit />);
      
      const performanceMetrics = screen.getByTestId('performance-metrics');
      expect(performanceMetrics).toHaveTextContent('Response Time');
    });

    it('displays system health status', () => {
      renderWithProviders(<EnhancedCopilotKit />);
      
      expect(screen.getByTestId('system-health')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('displays error messages when API calls fail', async () => {
      // Mock API failure
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      renderWithProviders(<EnhancedCopilotKit />);
      
      // Simulate error condition
      fireEvent.click(screen.getByTestId('trigger-error'));
      
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
      
      consoleSpy.mockRestore();
    });

    it('provides retry functionality on errors', async () => {
      renderWithProviders(<EnhancedCopilotKit />);
      
      // Simulate error and retry
      fireEvent.click(screen.getByTestId('trigger-error'));
      
      await waitFor(() => {
        const retryButton = screen.getByText(/retry/i);
        expect(retryButton).toBeInTheDocument();
        fireEvent.click(retryButton);
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      renderWithProviders(<EnhancedCopilotKit />);
      
      expect(screen.getByLabelText(/ai assistant/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/voice recognition/i)).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<EnhancedCopilotKit />);
      
      // Test tab navigation
      await user.tab();
      expect(screen.getByTestId('voice-recognition-button')).toHaveFocus();
      
      await user.tab();
      expect(screen.getByTestId('settings-button')).toHaveFocus();
    });

    it('has proper color contrast', () => {
      renderWithProviders(<EnhancedCopilotKit />);
      
      const component = screen.getByTestId('enhanced-copilotkit');
      const styles = window.getComputedStyle(component);
      
      // Basic contrast check (would need more sophisticated testing in real scenario)
      expect(styles.color).toBeDefined();
      expect(styles.backgroundColor).toBeDefined();
    });
  });

  describe('Integration with Hooks', () => {
    it('responds to authentication changes', () => {
      const { rerender } = renderWithProviders(<EnhancedCopilotKit />);
      
      // Change auth state
      mockUseAuth.mockReturnValue({
        user: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        refreshToken: jest.fn(),
      });
      
      rerender(
        <QueryClientProvider client={queryClient}>
          <EnhancedCopilotKit />
        </QueryClientProvider>
      );
      
      expect(screen.getByText(/please log in/i)).toBeInTheDocument();
    });

    it('adapts to tenant permissions', () => {
      mockUsePermissions.mockReturnValue({
        permissions: ['read'],
        hasPermission: jest.fn().mockReturnValue(false),
        hasAnyPermission: jest.fn().mockReturnValue(false),
        hasAllPermissions: jest.fn().mockReturnValue(false),
        canAccess: jest.fn().mockReturnValue(false),
      });
      
      renderWithProviders(<EnhancedCopilotKit />);
      
      expect(screen.getByText(/insufficient permissions/i)).toBeInTheDocument();
    });
  });
});
