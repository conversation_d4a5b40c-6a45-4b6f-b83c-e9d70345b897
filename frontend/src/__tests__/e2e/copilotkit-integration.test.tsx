import { test, expect } from '@playwright/test';

test.describe('CopilotKit Integration E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForLoadState('networkidle');
  });

  test('should load CopilotKit assistant', async ({ page }) => {
    // Check if CopilotKit trigger button is visible
    const copilotTrigger = page.locator('[data-testid="copilot-trigger"]');
    await expect(copilotTrigger).toBeVisible();
    
    // Click to open the assistant
    await copilotTrigger.click();
    
    // Check if the chat interface opens
    const chatInterface = page.locator('[data-testid="copilot-chat"]');
    await expect(chatInterface).toBeVisible();
  });

  test('should handle basic chat interaction', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Type a message
    const chatInput = page.locator('[data-testid="copilot-input"]');
    await chatInput.fill('What is my revenue for last month?');
    
    // Send the message
    await page.keyboard.press('Enter');
    
    // Wait for response
    const response = page.locator('[data-testid="copilot-response"]').first();
    await expect(response).toBeVisible({ timeout: 10000 });
    
    // Check if response contains expected content
    await expect(response).toContainText('revenue');
  });

  test('should display analytics visualization', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Ask for analytics
    const chatInput = page.locator('[data-testid="copilot-input"]');
    await chatInput.fill('Show me revenue analytics');
    await page.keyboard.press('Enter');
    
    // Wait for visualization to appear
    const chart = page.locator('[data-testid="revenue-chart"]');
    await expect(chart).toBeVisible({ timeout: 15000 });
    
    // Check if chart has data
    const chartData = page.locator('[data-testid="chart-data-point"]');
    await expect(chartData.first()).toBeVisible();
  });

  test('should handle voice recognition', async ({ page }) => {
    // Grant microphone permissions (in real test, this would be configured)
    await page.context().grantPermissions(['microphone']);
    
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Click voice recognition button
    const voiceButton = page.locator('[data-testid="voice-recognition-button"]');
    await voiceButton.click();
    
    // Check if voice recognition is active
    const voiceIndicator = page.locator('[data-testid="voice-active-indicator"]');
    await expect(voiceIndicator).toBeVisible();
  });

  test('should display performance metrics', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Check if performance metrics are visible
    const performanceMetrics = page.locator('[data-testid="performance-metrics"]');
    await expect(performanceMetrics).toBeVisible();
    
    // Check specific metrics
    const responseTime = page.locator('[data-testid="response-time-metric"]');
    await expect(responseTime).toBeVisible();
    
    const systemHealth = page.locator('[data-testid="system-health-metric"]');
    await expect(systemHealth).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Mock network failure
    await page.route('**/api/v1/copilotkit/**', route => {
      route.abort('failed');
    });
    
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Try to send a message
    const chatInput = page.locator('[data-testid="copilot-input"]');
    await chatInput.fill('Test message');
    await page.keyboard.press('Enter');
    
    // Check if error message is displayed
    const errorMessage = page.locator('[data-testid="error-message"]');
    await expect(errorMessage).toBeVisible({ timeout: 5000 });
    
    // Check if retry button is available
    const retryButton = page.locator('[data-testid="retry-button"]');
    await expect(retryButton).toBeVisible();
  });

  test('should support keyboard navigation', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Test tab navigation
    await page.keyboard.press('Tab');
    
    // Check if voice button is focused
    const voiceButton = page.locator('[data-testid="voice-recognition-button"]');
    await expect(voiceButton).toBeFocused();
    
    // Continue tabbing
    await page.keyboard.press('Tab');
    
    // Check if settings button is focused
    const settingsButton = page.locator('[data-testid="settings-button"]');
    await expect(settingsButton).toBeFocused();
  });

  test('should handle streaming responses', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Ask a question that triggers streaming
    const chatInput = page.locator('[data-testid="copilot-input"]');
    await chatInput.fill('Generate a detailed market analysis report');
    await page.keyboard.press('Enter');
    
    // Check if streaming indicator appears
    const streamingIndicator = page.locator('[data-testid="streaming-indicator"]');
    await expect(streamingIndicator).toBeVisible();
    
    // Wait for streaming to complete
    await expect(streamingIndicator).toBeHidden({ timeout: 30000 });
    
    // Check if final response is displayed
    const finalResponse = page.locator('[data-testid="copilot-response"]').last();
    await expect(finalResponse).toBeVisible();
  });

  test('should display user and tenant information', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Check if user info is displayed
    const userInfo = page.locator('[data-testid="user-info"]');
    await expect(userInfo).toBeVisible();
    
    // Check if tenant info is displayed
    const tenantInfo = page.locator('[data-testid="tenant-info"]');
    await expect(tenantInfo).toBeVisible();
  });

  test('should handle location analysis requests', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Request location analysis
    const chatInput = page.locator('[data-testid="copilot-input"]');
    await chatInput.fill('Analyze location at 123 Main Street for a new restaurant');
    await page.keyboard.press('Enter');
    
    // Wait for location analysis result
    const locationAnalysis = page.locator('[data-testid="location-analysis-result"]');
    await expect(locationAnalysis).toBeVisible({ timeout: 15000 });
    
    // Check if analysis contains expected elements
    const demographicsSection = page.locator('[data-testid="demographics-section"]');
    await expect(demographicsSection).toBeVisible();
    
    const competitionSection = page.locator('[data-testid="competition-section"]');
    await expect(competitionSection).toBeVisible();
  });

  test('should handle report generation workflow', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Request report generation
    const chatInput = page.locator('[data-testid="copilot-input"]');
    await chatInput.fill('Generate a comprehensive market analysis report');
    await page.keyboard.press('Enter');
    
    // Wait for report generation to start
    const reportProgress = page.locator('[data-testid="report-progress"]');
    await expect(reportProgress).toBeVisible({ timeout: 10000 });
    
    // Wait for report to complete
    const reportResult = page.locator('[data-testid="report-result"]');
    await expect(reportResult).toBeVisible({ timeout: 30000 });
    
    // Check if report has expected sections
    const executiveSummary = page.locator('[data-testid="executive-summary"]');
    await expect(executiveSummary).toBeVisible();
    
    const keyFindings = page.locator('[data-testid="key-findings"]');
    await expect(keyFindings).toBeVisible();
    
    const recommendations = page.locator('[data-testid="recommendations"]');
    await expect(recommendations).toBeVisible();
  });

  test('should maintain conversation context', async ({ page }) => {
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Send first message
    const chatInput = page.locator('[data-testid="copilot-input"]');
    await chatInput.fill('What was my revenue last month?');
    await page.keyboard.press('Enter');
    
    // Wait for first response
    await page.waitForSelector('[data-testid="copilot-response"]');
    
    // Send follow-up message that requires context
    await chatInput.fill('How does that compare to the previous month?');
    await page.keyboard.press('Enter');
    
    // Wait for contextual response
    const contextualResponse = page.locator('[data-testid="copilot-response"]').nth(1);
    await expect(contextualResponse).toBeVisible({ timeout: 10000 });
    
    // Response should reference previous context
    await expect(contextualResponse).toContainText(/compare|comparison|previous/);
  });

  test('should handle permissions and access control', async ({ page }) => {
    // Test with limited permissions user (would need to be set up in test environment)
    await page.goto('/?user=limited');
    
    // Open CopilotKit
    await page.click('[data-testid="copilot-trigger"]');
    
    // Try to access restricted functionality
    const chatInput = page.locator('[data-testid="copilot-input"]');
    await chatInput.fill('Generate financial report with sensitive data');
    await page.keyboard.press('Enter');
    
    // Should show permission error
    const permissionError = page.locator('[data-testid="permission-error"]');
    await expect(permissionError).toBeVisible({ timeout: 5000 });
  });
});
