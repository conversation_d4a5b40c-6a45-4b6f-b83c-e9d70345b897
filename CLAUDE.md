# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Quick Start
```bash
# One-command setup (starts both frontend and backend)
./start.sh                # Development mode
./start.sh prod           # Production mode

# Docker setup
docker-compose up -d      # Full stack with monitoring
docker-compose --profile logging up -d  # Include ELK logging stack
```

### Frontend Commands (from /frontend)
```bash
# Development
npm run dev               # Start dev server on port 3000 (default)
npm run dev:turbo         # Start with Turbopack on port 3000
npm run dev:original      # Original dev server on port 50513 with turbopack
npm run build             # Production build
npm run start             # Production server on port 3000
npm run start:prod        # Production server on port 52580
npm run staging           # Staging server on port 12001

# Code Quality
npm run lint              # ESLint validation
npm run lint:fix          # Auto-fix ESLint issues
npm run check-types       # TypeScript type checking
npm run clean             # Clean build artifacts and cache

# Testing
npm test                  # Run Jest tests (10s timeout)
npm run test:watch        # Watch mode
npm run test:coverage     # Coverage report

# Deployment
npm run deploy            # Deploy to Vercel production
npm run deploy:beta       # Deploy to beta environment
npm run preview           # Preview deployment
```

### Testing Commands
```bash
# Frontend Testing (from /frontend)
npm test                  # Jest tests with 10s timeout, jsdom environment
npm run test:watch        # Jest in watch mode
npm run test:coverage     # Coverage report with HTML and XML output

# Backend Testing (from /backend)
pytest                    # Run all tests with coverage
pytest --cov=app tests/   # Explicit coverage
pytest -m "not slow"      # Skip slow tests
pytest -m unit           # Run only unit tests
pytest -m integration    # Run only integration tests
pytest -m api            # Run only API tests
pytest --tb=short        # Short traceback format
```

### Backend Commands (from /backend)
```bash
# Setup
python3 -m venv venv && source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# Development
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Alternative entry points
python -m app.main        # Direct Python execution
python app/main_simple.py # Simplified main entry point
```

## High-Level Architecture

### Full-Stack Structure
- **Frontend**: Next.js 15.4.4 with App Router, TypeScript, Tailwind CSS v4, React 18.3.1
- **Backend**: FastAPI with SQLAlchemy 2.0, PostgreSQL/SQLite, Redis, Python 3.8+
- **AI/Analytics**: Restaurant intelligence platform with location-based insights and CopilotKit integration
- **DevOps**: Docker Compose, Prometheus/Grafana monitoring, optional ELK stack
- **State Management**: Zustand for global state, TanStack Query v5 for server state
- **UI Components**: Radix UI primitives with custom design system and Tailwind CSS
- **Charts**: Chart.js v4, Recharts v2, D3.js v7 for comprehensive data visualization
- **Maps**: Mapbox GL v3 and Leaflet v1.9 for location intelligence
- **Testing**: Jest v29 (frontend), Pytest (backend) with comprehensive coverage
- **Payments**: Stripe integration with React components

### Core Domain Areas

#### 1. Restaurant Intelligence & Analytics
The platform's core purpose is providing AI-powered restaurant intelligence:
- **Location Intelligence**: Geospatial analysis for restaurant placement and market research
- **Product Intelligence**: Menu optimization, pricing analysis, food cost tracking
- **Market Research**: Competitive analysis, trend identification, customer behavior insights
- **Performance Analytics**: Revenue optimization, operational efficiency metrics

#### 2. Multi-Modal Data Processing
- **Natural Language Query**: Voice and text-based data querying with confidence scoring
- **Real-time Analytics**: WebSocket-based live data streams and dashboard updates
- **Data Connectors**: SQL/NoSQL database integrations with unified query interface
- **Visualization Engine**: Chart.js, D3.js, Mapbox integration for interactive dashboards

#### 3. Enterprise Features
- **Multi-location Management**: Centralized control for restaurant chains
- **POS Integration**: Point-of-sale system connectivity and data synchronization
- **Collaboration Tools**: Real-time team collaboration with presence tracking
- **Campaign Management**: Marketing automation and A/B testing capabilities

### Key Technical Patterns

#### Frontend Architecture (Next.js 15.4.4)
- **Component Organization**: Feature-based structure with domain-driven design (`/components/dashboard/`, `/components/ai/`, `/components/location/`, etc.)
- **State Management**: Zustand v4 for global state, TanStack Query v5 for server state caching
- **Chart System**: Centralized chart registry with theme providers and cross-filtering (Chart.js v4, Recharts v2, D3.js v7)
- **Authentication Flow**: JWT-based auth with protected routes, role guards, and automatic token refresh
- **API Communication**: Centralized API client (`/lib/api-client.ts`) with typed endpoints and error handling
- **UI Framework**: Radix UI components with Tailwind CSS v4, custom design system, and animation components
- **Performance**: Turbopack enabled, strategic bundle splitting, lazy loading, and image optimization
- **Real-time Features**: WebSocket connections for live collaboration and analytics updates

#### Backend Architecture (FastAPI)
- **Service Layer Pattern**: Modular services (`/services/analytics/`, `/services/location/`, `/services/ai/`, etc.)
- **Database Strategy**: SQLAlchemy 2.0 with async support, GeoAlchemy2 for spatial data
- **API Design**: RESTful endpoints (`/api/v1/`) with comprehensive error handling
- **Connector Architecture**: Pluggable data source connectors (SQL/NoSQL) with base classes
- **Middleware**: CORS, compression, API monitoring, rate limiting, security headers
- **Health Monitoring**: Health checks, Prometheus metrics, structured logging

#### AI/ML Integration Points
- **Natural Language Processing**: Intent classification, entity extraction, SQL generation
- **Location Intelligence**: Geocoding, market analysis, demographic insights
- **Predictive Analytics**: Revenue forecasting, trend analysis, anomaly detection
- **Research Agent**: Market intelligence gathering and competitive analysis

### Critical Integration Points

#### Frontend ↔ Backend Communication
- **API Client**: Centralized client in `src/lib/api-client.ts` with typed endpoints and comprehensive error handling
- **Real-time Updates**: WebSocket connections for live dashboard updates and collaboration features
- **Error Handling**: Unified error handling with toast notifications and proper HTTP status codes
- **Authentication**: JWT token management with automatic refresh and secure localStorage persistence
- **Request/Response Flow**: Typed interfaces for all API requests and responses with Zod validation
- **API Proxy**: Next.js rewrites for seamless API communication during development

#### Data Flow Architecture
1. **External Data Sources** → Connector Services → Database
2. **Database** → Analytics Services → API Endpoints
3. **API Endpoints** → Frontend Components → User Interface
4. **User Actions** → API Requests → Service Layer → Database Updates

#### Key Database Entities
- **Restaurants**: Core restaurant data with location and operational info
- **Analytics**: Performance metrics, revenue data, customer insights
- **Locations**: Geospatial data for market analysis and placement optimization
- **Users**: Authentication, roles, multi-tenant access control

### Development Workflow Considerations

#### Testing Strategy
- **Frontend**: Jest v29 with React Testing Library, 10s timeout, jsdom environment, 70% coverage threshold
- **Backend**: Pytest with async support, coverage reporting, categorized test markers (unit, integration, api, slow, performance, security, collaboration)
- **Integration**: API endpoint testing, database integration tests, real-time collaboration testing
- **E2E Testing**: Playwright tests for critical user journeys

#### Performance Optimizations
- **Frontend**: Turbopack, strategic bundle splitting, lazy loading, virtual scrolling
- **Backend**: Async/await patterns, database query optimization, Redis caching
- **Charts**: Optimized rendering with memoization and data aggregation

#### Monitoring & Observability
- **Health Checks**: `/health` endpoints for both frontend and backend
- **Metrics**: Prometheus integration with Grafana dashboards
- **Logging**: Structured logging with optional ELK stack integration
- **Error Tracking**: Centralized error handling and reporting

### Environment & Configuration

#### Key Environment Variables
```bash
# Backend
DATABASE_URL=sqlite:///./bitebase_intelligence.db
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
ENVIRONMENT=development
DEBUG=true
RATE_LIMIT_ENABLED=true
MONITORING_ENABLED=true

# Frontend  
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-key-here
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_PUBLIC_APP_NAME="BiteBase Intelligence"
NEXT_PUBLIC_APP_VERSION=2.1.0
```

#### Port Allocation
- Frontend Dev: 3000 (default), Legacy: 50513, Staging: 12001, Production: 52580
- Backend: 8000
- PostgreSQL: 5432, Redis: 6379
- Monitoring: Prometheus 9090, Grafana 3001
- Logging: Elasticsearch 9200, Kibana 5601, Logstash 5044
- Docker Frontend: 80/443 (with nginx)

### Key Component Directories

#### Frontend Component Architecture (`/frontend/src/components/`)
- **`/dashboard/`**: Main dashboard components, tabs, and business intelligence hub
  - BusinessIntelligenceHub, DashboardBuilder, EnhancedDashboard
  - Tabs: AIInsightsTab, AnalyticsTab, LocationIntelligenceTab, MarketAnalysisTab
- **`/ai/`**: AI and chatbot components
  - EnhancedBiteBaseAI, AIResearchAgentPage, FloatingChatbot
- **`/charts/`**: Comprehensive charting system
  - Core: BaseChart, ChartRegistry, ChartContainer
  - Basic: LineChart, BarChart, PieChart, AreaChart, DoughnutChart
  - Advanced: TreeMapChart for hierarchical data
  - Providers: ThemeProvider, CrossFilterProvider
- **`/nl-query/`**: Natural language query interface
  - NaturalLanguageQueryInterface, VoiceInput, QueryResults, ConfidenceIndicator
- **`/location/`**: Location intelligence and mapping
  - InteractiveMap, LocationIntelligencePage, MarketResearchPanel
- **`/connectors/`**: Data source integration components
  - ConnectorWizard, DataSourcesPage, ConnectionForm, TestConnection
- **`/collaboration/`**: Real-time collaboration features
  - RealtimeCollaboration, PresenceIndicators, CommentSystem, VersionHistory
- **`/auth/`**: Authentication and authorization components
  - AuthProvider, ProtectedRoute, RoleGuard, UserMenu

#### Backend Service Architecture (`/backend/app/services/`)
- **`/analytics/`**: Analytics and performance services
  - `analytics_service.py`, `integrated_analytics_service.py`
- **`/location/`**: Geocoding and location intelligence services
  - `geocoding_service.py`, `location_intelligence_service.py`
- **`/ai/`**: Advanced ML pipeline and AI processing
  - `advanced_ml_pipeline.py`
- **`/restaurant/`**: Restaurant management and operations
  - `restaurant_service.py`, `restaurant_management_service.py`
- **`/menu/`**: Menu intelligence and optimization
  - `menu_intelligence_service.py`
- **`/connectors/`**: Data source connectors with base classes
  - `/base/`: Base connector classes and exceptions
  - `/sql/`: MySQL, PostgreSQL connectors
  - `/nosql/`: MongoDB connector
  - `/management/`: Connection manager and registry
- **`/nl_query/`**: Natural language processing services
  - Intent classification, entity extraction, SQL generation, confidence scoring
- **`/insights/`**: Business insights and pattern analysis
  - `insights_engine.py`, `anomaly_detector.py`, `pattern_analyzer.py`
- **`/security/`**: Enterprise security, RBAC, audit services
  - `rbac_service.py`, `audit_service.py`, `enterprise_rbac.py`
- **`/collaboration/`**: Real-time sync and presence tracking
  - `realtime_sync.py`, `presence_tracker.py`
- **`/dashboard/`**: Dashboard and visualization services
  - `dashboard_service.py`, `visualization_engine.py`
- **`/campaign/`**: Marketing campaign management
- **`/pos/`**: Point-of-sale system integration
- **`/multi_location/`**: Multi-location restaurant management
- **`/performance/`**: Caching and query optimization
- **`/monitoring/`**: API monitoring and observability

### Development Patterns and Best Practices

#### Testing Strategy
- **Frontend**: Jest v29 with React Testing Library, 10s timeout, jsdom environment
  - Coverage: Components, hooks, and lib files with HTML/XML reporting (70% threshold)
  - Module mapping: `@/*` aliases resolved to `src/*`
  - Config: `jest.config.js` with Next.js integration and comprehensive test patterns
- **Backend**: Pytest with async support and comprehensive markers
  - Markers: `unit`, `integration`, `api`, `slow`, `performance`, `security`, `collaboration`
  - Coverage: HTML, XML, and terminal reporting with `--cov=app`
  - Auto-detection of asyncio tests with `asyncio_mode = auto`
  - Config: `pytest.ini` with strict configuration and JUnit XML output

#### API Integration Patterns
- **Typed Interfaces**: All API requests/responses have TypeScript interfaces
- **Error Handling**: Consistent error response format with proper HTTP status codes
- **Authentication**: JWT tokens with automatic refresh and proper storage
- **Health Checks**: Both frontend (`/api/health`) and backend (`/health`) endpoints

#### Performance Considerations
- **Frontend**: Turbopack for development, strategic bundle splitting, lazy loading
- **Backend**: Async/await patterns, connection pooling, Redis caching
- **Charts**: Optimized rendering with memoization and data aggregation
- **Database**: Query optimization, proper indexing, connection management

### Project Startup & Troubleshooting

#### Startup Scripts
The project includes several startup scripts for different scenarios:
- **`./start.sh`**: Main development startup script (recommended)
- **`./start-simple.sh`**: Simplified startup for basic development
- **`./start-docker.sh`**: Docker-based development environment
- **`./start-services.sh`**: Advanced service management

#### Common Issues & Solutions
- **Port Conflicts**: Default ports are 3000 (frontend) and 8000 (backend). Scripts will detect conflicts.
- **Python Virtual Environment**: Scripts automatically create and manage venv in `/backend/venv/`
- **Package Installation**: Use `--legacy-peer-deps` flag for npm to avoid dependency conflicts
- **Database**: SQLite database file created automatically at `/backend/bitebase_intelligence.db`
- **Webpack/Build Issues**: Error boundary implemented with automatic recovery, simplified webpack config

#### Monitoring & Health Checks
- **Frontend Health**: `http://localhost:3000/api/health`
- **Backend Health**: `http://localhost:8000/health`
- **API Documentation**: `http://localhost:8000/docs` (Swagger UI)
- **Monitoring Stack**: Prometheus (9090), Grafana (3001) when using Docker

#### Key Configuration Files
- **Frontend Config**: `next.config.ts` with performance optimizations and security headers
- **Backend Config**: `app/core/config.py` for application settings
- **Docker Config**: `docker-compose.yml` for full-stack deployment
- **Testing Config**: `jest.config.js` (frontend), `pytest.ini` (backend)