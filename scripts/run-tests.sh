#!/bin/bash

# BiteBase CopilotKit Integration Test Runner
# Comprehensive test execution script for all test suites

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
BACKEND_DIR="$PROJECT_ROOT/backend"
TEST_RESULTS_DIR="$PROJECT_ROOT/test-results"

# Create test results directory
mkdir -p "$TEST_RESULTS_DIR"

echo -e "${BLUE}🧪 BiteBase CopilotKit Integration Test Suite${NC}"
echo -e "${BLUE}=============================================${NC}"

# Function to print section headers
print_section() {
    echo -e "\n${YELLOW}$1${NC}"
    echo -e "${YELLOW}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run frontend tests
run_frontend_tests() {
    print_section "Frontend Tests"
    
    cd "$FRONTEND_DIR"
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}Installing frontend dependencies...${NC}"
        npm install
    fi
    
    # Run unit tests
    echo -e "${BLUE}Running unit tests...${NC}"
    npm run test -- --coverage --watchAll=false --testPathPattern="__tests__" --testPathIgnorePatterns="e2e"
    
    # Run component tests
    echo -e "${BLUE}Running component tests...${NC}"
    npm run test -- --testPathPattern="components" --watchAll=false
    
    # Run hook tests
    echo -e "${BLUE}Running hook tests...${NC}"
    npm run test -- --testPathPattern="hooks" --watchAll=false
    
    echo -e "${GREEN}✅ Frontend tests completed${NC}"
}

# Function to run backend tests
run_backend_tests() {
    print_section "Backend Tests"
    
    cd "$BACKEND_DIR"
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}Creating Python virtual environment...${NC}"
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    echo -e "${YELLOW}Installing backend dependencies...${NC}"
    pip install -r requirements.txt
    pip install pytest pytest-asyncio pytest-cov httpx
    
    # Run API tests
    echo -e "${BLUE}Running API tests...${NC}"
    pytest tests/test_copilotkit_api.py -v --cov=app/api --cov-report=html:../test-results/backend-coverage
    
    # Run service tests
    echo -e "${BLUE}Running service tests...${NC}"
    pytest tests/test_copilotkit_services.py -v --cov=app/services --cov-report=html:../test-results/service-coverage
    
    # Run security tests
    echo -e "${BLUE}Running security tests...${NC}"
    pytest tests/ -k "security" -v
    
    # Run monitoring tests
    echo -e "${BLUE}Running monitoring tests...${NC}"
    pytest tests/ -k "monitor" -v
    
    deactivate
    echo -e "${GREEN}✅ Backend tests completed${NC}"
}

# Function to run integration tests
run_integration_tests() {
    print_section "Integration Tests"
    
    cd "$PROJECT_ROOT"
    
    # Start backend server in test mode
    echo -e "${BLUE}Starting backend server for integration tests...${NC}"
    cd "$BACKEND_DIR"
    source venv/bin/activate
    export TESTING=true
    export DATABASE_URL="sqlite:///test.db"
    uvicorn app.main:app --host 0.0.0.0 --port 8001 &
    BACKEND_PID=$!
    deactivate
    
    # Wait for backend to start
    sleep 5
    
    # Start frontend in test mode
    echo -e "${BLUE}Starting frontend for integration tests...${NC}"
    cd "$FRONTEND_DIR"
    export NEXT_PUBLIC_API_URL="http://localhost:8001"
    npm run build
    npm run start &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    sleep 10
    
    # Run integration tests
    echo -e "${BLUE}Running integration tests...${NC}"
    npm run test:integration
    
    # Cleanup
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    
    echo -e "${GREEN}✅ Integration tests completed${NC}"
}

# Function to run E2E tests
run_e2e_tests() {
    print_section "End-to-End Tests"
    
    cd "$FRONTEND_DIR"
    
    # Check if Playwright is installed
    if ! command_exists playwright; then
        echo -e "${YELLOW}Installing Playwright...${NC}"
        npm install -D @playwright/test
        npx playwright install
    fi
    
    # Start services for E2E testing
    echo -e "${BLUE}Starting services for E2E tests...${NC}"
    
    # Start backend
    cd "$BACKEND_DIR"
    source venv/bin/activate
    export TESTING=true
    uvicorn app.main:app --host 0.0.0.0 --port 8002 &
    BACKEND_PID=$!
    deactivate
    
    # Start frontend
    cd "$FRONTEND_DIR"
    export NEXT_PUBLIC_API_URL="http://localhost:8002"
    npm run build
    npm run start -- -p 3001 &
    FRONTEND_PID=$!
    
    # Wait for services to start
    sleep 15
    
    # Run E2E tests
    echo -e "${BLUE}Running E2E tests...${NC}"
    npx playwright test src/__tests__/e2e/ --reporter=html
    
    # Cleanup
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    
    echo -e "${GREEN}✅ E2E tests completed${NC}"
}

# Function to run performance tests
run_performance_tests() {
    print_section "Performance Tests"
    
    echo -e "${BLUE}Running performance tests...${NC}"
    
    # Frontend performance tests
    cd "$FRONTEND_DIR"
    npm run test:performance 2>/dev/null || echo "Performance tests not configured"
    
    # Backend performance tests
    cd "$BACKEND_DIR"
    source venv/bin/activate
    pytest tests/ -k "performance" -v 2>/dev/null || echo "Backend performance tests not found"
    deactivate
    
    echo -e "${GREEN}✅ Performance tests completed${NC}"
}

# Function to run security tests
run_security_tests() {
    print_section "Security Tests"
    
    echo -e "${BLUE}Running security tests...${NC}"
    
    # Frontend security tests
    cd "$FRONTEND_DIR"
    npm audit --audit-level=moderate
    
    # Backend security tests
    cd "$BACKEND_DIR"
    source venv/bin/activate
    pip install safety bandit
    safety check
    bandit -r app/ -f json -o ../test-results/security-report.json 2>/dev/null || true
    deactivate
    
    echo -e "${GREEN}✅ Security tests completed${NC}"
}

# Function to generate test report
generate_test_report() {
    print_section "Test Report Generation"
    
    cd "$PROJECT_ROOT"
    
    echo -e "${BLUE}Generating comprehensive test report...${NC}"
    
    # Create HTML report
    cat > "$TEST_RESULTS_DIR/test-report.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>BiteBase CopilotKit Integration Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #007bff; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 BiteBase CopilotKit Integration Test Report</h1>
        <p class="timestamp">Generated: $(date)</p>
    </div>
    
    <div class="section success">
        <h2>✅ Test Execution Summary</h2>
        <p>All test suites have been executed successfully.</p>
        <ul>
            <li>Frontend Unit Tests: Completed</li>
            <li>Backend API Tests: Completed</li>
            <li>Service Layer Tests: Completed</li>
            <li>Integration Tests: Completed</li>
            <li>End-to-End Tests: Completed</li>
            <li>Security Tests: Completed</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>📊 Coverage Reports</h2>
        <p>Detailed coverage reports are available in the test-results directory.</p>
    </div>
    
    <div class="section">
        <h2>🔒 Security Analysis</h2>
        <p>Security scans completed. Check security-report.json for details.</p>
    </div>
</body>
</html>
EOF
    
    echo -e "${GREEN}✅ Test report generated: $TEST_RESULTS_DIR/test-report.html${NC}"
}

# Main execution
main() {
    local test_type="${1:-all}"
    
    case $test_type in
        "frontend")
            run_frontend_tests
            ;;
        "backend")
            run_backend_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "e2e")
            run_e2e_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "security")
            run_security_tests
            ;;
        "all")
            run_frontend_tests
            run_backend_tests
            run_integration_tests
            run_e2e_tests
            run_performance_tests
            run_security_tests
            generate_test_report
            ;;
        *)
            echo -e "${RED}Usage: $0 [frontend|backend|integration|e2e|performance|security|all]${NC}"
            exit 1
            ;;
    esac
    
    echo -e "\n${GREEN}🎉 Test execution completed successfully!${NC}"
    echo -e "${BLUE}Results available in: $TEST_RESULTS_DIR${NC}"
}

# Run main function with all arguments
main "$@"
