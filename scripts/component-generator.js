#!/usr/bin/env node

/**
 * BiteBase Intelligence - Component Generator
 * 
 * Automated component generation with templates, tests, and documentation
 * Supports dashboard widgets, chart components, and business logic components
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const chalk = require('chalk');
const inquirer = require('inquirer');

class ComponentGenerator {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.frontendPath = path.join(this.projectRoot, 'frontend');
    this.templatesPath = path.join(__dirname, 'templates');
    this.componentsPath = path.join(this.frontendPath, 'src/components');
  }

  /**
   * Main generator workflow
   */
  async generate() {
    console.log(chalk.blue.bold('🚀 BiteBase Intelligence - Component Generator\n'));

    try {
      const answers = await this.promptUser();
      await this.generateComponent(answers);
      await this.generateTests(answers);
      await this.generateDocumentation(answers);
      await this.updateIndex(answers);
      
      console.log(chalk.green.bold('\n✅ Component generation complete!'));
      console.log(chalk.cyan(`📁 Generated files in: ${answers.componentPath}`));
      
    } catch (error) {
      console.error(chalk.red('Error during generation:'), error.message);
      process.exit(1);
    }
  }

  /**
   * Interactive prompts for component configuration
   */
  async promptUser() {
    const questions = [
      {
        type: 'list',
        name: 'componentType',
        message: 'What type of component would you like to create?',
        choices: [
          { name: '📊 Dashboard Widget', value: 'dashboard-widget' },
          { name: '📈 Chart Component', value: 'chart-component' },
          { name: '🔍 Analytics Component', value: 'analytics-component' },
          { name: '🏪 Restaurant Component', value: 'restaurant-component' },
          { name: '🗺️ Location Component', value: 'location-component' },
          { name: '🤖 AI Component', value: 'ai-component' },
          { name: '📄 Report Component', value: 'report-component' },
          { name: '🔧 Utility Component', value: 'utility-component' },
          { name: '🎨 UI Component', value: 'ui-component' }
        ]
      },
      {
        type: 'input',
        name: 'componentName',
        message: 'Component name (PascalCase):',
        validate: (input) => {
          if (!/^[A-Z][a-zA-Z0-9]*$/.test(input)) {
            return 'Component name must be in PascalCase (e.g., MyComponent)';
          }
          return true;
        }
      },
      {
        type: 'input',
        name: 'description',
        message: 'Component description:'
      },
      {
        type: 'confirm',
        name: 'includeProps',
        message: 'Include TypeScript interface for props?',
        default: true
      },
      {
        type: 'confirm',
        name: 'includeHooks',
        message: 'Include custom hooks?',
        default: false
      },
      {
        type: 'confirm',
        name: 'includeTests',
        message: 'Generate test files?',
        default: true
      },
      {
        type: 'confirm',
        name: 'includeStorybook',
        message: 'Generate Storybook stories?',
        default: false
      },
      {
        type: 'list',
        name: 'styleType',
        message: 'Styling approach:',
        choices: [
          { name: 'Tailwind CSS (recommended)', value: 'tailwind' },
          { name: 'CSS Modules', value: 'css-modules' },
          { name: 'Styled Components', value: 'styled-components' },
          { name: 'No styling', value: 'none' }
        ],
        default: 'tailwind'
      }
    ];

    const answers = await inquirer.prompt(questions);

    // Determine component path based on type
    const typeToPath = {
      'dashboard-widget': 'dashboard/widgets',
      'chart-component': 'charts/custom',
      'analytics-component': 'analytics',
      'restaurant-component': 'restaurant',
      'location-component': 'location',
      'ai-component': 'ai',
      'report-component': 'reports',
      'utility-component': 'utils',
      'ui-component': 'ui'
    };

    answers.componentPath = path.join(this.componentsPath, typeToPath[answers.componentType]);
    answers.fileName = answers.componentName;
    
    return answers;
  }

  /**
   * Generate the main component file
   */
  async generateComponent(config) {
    console.log(chalk.yellow('🔧 Generating component...'));

    // Ensure directory exists
    if (!fs.existsSync(config.componentPath)) {
      fs.mkdirSync(config.componentPath, { recursive: true });
    }

    const template = this.getTemplate(config.componentType);
    const componentContent = this.processTemplate(template, config);
    
    const filePath = path.join(config.componentPath, `${config.fileName}.tsx`);
    fs.writeFileSync(filePath, componentContent);

    // Generate props interface file if requested
    if (config.includeProps) {
      const propsTemplate = this.getPropsTemplate(config);
      const propsPath = path.join(config.componentPath, `${config.fileName}.types.ts`);
      fs.writeFileSync(propsPath, propsTemplate);
    }

    // Generate custom hook if requested
    if (config.includeHooks) {
      const hookTemplate = this.getHookTemplate(config);
      const hookPath = path.join(config.componentPath, `use${config.fileName}.ts`);
      fs.writeFileSync(hookPath, hookTemplate);
    }

    console.log(chalk.green('✅ Component generated'));
  }

  /**
   * Generate test files
   */
  async generateTests(config) {
    if (!config.includeTests) return;

    console.log(chalk.yellow('🧪 Generating tests...'));

    const testTemplate = this.getTestTemplate(config);
    const testPath = path.join(config.componentPath, `${config.fileName}.test.tsx`);
    fs.writeFileSync(testPath, testTemplate);

    console.log(chalk.green('✅ Tests generated'));
  }

  /**
   * Generate documentation
   */
  async generateDocumentation(config) {
    console.log(chalk.yellow('📚 Generating documentation...'));

    const docTemplate = this.getDocumentationTemplate(config);
    const docPath = path.join(config.componentPath, `${config.fileName}.md`);
    fs.writeFileSync(docPath, docTemplate);

    // Generate Storybook story if requested
    if (config.includeStorybook) {
      const storyTemplate = this.getStoryTemplate(config);
      const storyPath = path.join(config.componentPath, `${config.fileName}.stories.tsx`);
      fs.writeFileSync(storyPath, storyTemplate);
    }

    console.log(chalk.green('✅ Documentation generated'));
  }

  /**
   * Update index files for exports
   */
  async updateIndex(config) {
    console.log(chalk.yellow('📝 Updating index files...'));

    const indexPath = path.join(config.componentPath, 'index.ts');
    const exportStatement = `export { default as ${config.componentName} } from './${config.fileName}';\n`;

    if (fs.existsSync(indexPath)) {
      const currentContent = fs.readFileSync(indexPath, 'utf8');
      if (!currentContent.includes(exportStatement)) {
        fs.appendFileSync(indexPath, exportStatement);
      }
    } else {
      fs.writeFileSync(indexPath, exportStatement);
    }

    console.log(chalk.green('✅ Index files updated'));
  }

  /**
   * Get component template based on type
   */
  getTemplate(componentType) {
    const templates = {
      'dashboard-widget': this.getDashboardWidgetTemplate(),
      'chart-component': this.getChartComponentTemplate(),
      'analytics-component': this.getAnalyticsComponentTemplate(),
      'restaurant-component': this.getRestaurantComponentTemplate(),
      'location-component': this.getLocationComponentTemplate(),
      'ai-component': this.getAIComponentTemplate(),
      'report-component': this.getReportComponentTemplate(),
      'utility-component': this.getUtilityComponentTemplate(),
      'ui-component': this.getUIComponentTemplate()
    };

    return templates[componentType] || templates['utility-component'];
  }

  /**
   * Dashboard Widget Template
   */
  getDashboardWidgetTemplate() {
    return `"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshCw, TrendingUp, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
{{#if includeProps}}
import { {{componentName}}Props } from './{{fileName}}.types';
{{/if}}
{{#if includeHooks}}
import { use{{componentName}} } from './use{{componentName}}';
{{/if}}

{{#if includeProps}}
const {{componentName}}: React.FC<{{componentName}}Props> = ({
  className,
  title = "{{description}}",
  refreshInterval = 30000,
  ...props
}) => {
{{else}}
interface {{componentName}}Props {
  className?: string;
  title?: string;
  refreshInterval?: number;
}

const {{componentName}}: React.FC<{{componentName}}Props> = ({
  className,
  title = "{{description}}",
  refreshInterval = 30000
}) => {
{{/if}}
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

{{#if includeHooks}}
  const { data: hookData, loading, error: hookError, refresh } = use{{componentName}}();
{{/if}}

  // Fetch data function
  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockData = {
        value: Math.floor(Math.random() * 1000),
        trend: Math.random() > 0.5 ? 'up' : 'down',
        change: (Math.random() * 10).toFixed(1)
      };
      
      setData(mockData);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    fetchData();
    
    if (refreshInterval > 0) {
      const interval = setInterval(fetchData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval]);

  // Manual refresh handler
  const handleRefresh = () => {
    fetchData();
  };

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            className="mt-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <CardDescription>
            Last updated: {lastUpdated.toLocaleTimeString()}
          </CardDescription>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-2xl font-bold">
              {data?.value?.toLocaleString() || '---'}
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <TrendingUp className={cn(
                "h-4 w-4",
                data?.trend === 'up' ? "text-green-500" : "text-red-500"
              )} />
              <span>
                {data?.change}% from last period
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default {{componentName}};`;
  }

  /**
   * Chart Component Template
   */
  getChartComponentTemplate() {
    return `"use client";

import React, { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
{{#if includeProps}}
import { {{componentName}}Props } from './{{fileName}}.types';
{{/if}}

// Chart.js imports
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

{{#if includeProps}}
const {{componentName}}: React.FC<{{componentName}}Props> = ({
  data,
  title = "{{description}}",
  className,
  height = 300,
  chartType = 'bar',
  loading = false,
  ...props
}) => {
{{else}}
interface {{componentName}}Props {
  data: any[];
  title?: string;
  className?: string;
  height?: number;
  chartType?: 'bar' | 'line' | 'pie';
  loading?: boolean;
}

const {{componentName}}: React.FC<{{componentName}}Props> = ({
  data,
  title = "{{description}}",
  className,
  height = 300,
  chartType = 'bar',
  loading = false
}) => {
{{/if}}
  // Memoized chart data processing
  const chartData = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        labels: [],
        datasets: []
      };
    }

    // TODO: Process your data according to your needs
    return {
      labels: data.map(item => item.label || item.name),
      datasets: [
        {
          label: title,
          data: data.map(item => item.value),
          backgroundColor: 'rgba(255, 159, 64, 0.6)',
          borderColor: 'rgba(255, 159, 64, 1)',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false,
        }
      ]
    };
  }, [data, title]);

  // Memoized chart options
  const chartOptions: ChartOptions<'bar'> = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        display: true,
      },
      title: {
        display: false, // We use Card title instead
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        cornerRadius: 8,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    elements: {
      bar: {
        borderRadius: 4,
      },
    },
  }), []);

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>Loading chart data...</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="w-full" style={{ height }} />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
        <CardContent>
          <div 
            className="flex items-center justify-center text-muted-foreground"
            style={{ height }}
          >
            No data to display
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>
          Showing {data.length} data points
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div style={{ height }}>
          <Bar data={chartData} options={chartOptions} />
        </div>
      </CardContent>
    </Card>
  );
};

export default React.memo({{componentName}});`;
  }

  /**
   * Analytics Component Template
   */
  getAnalyticsComponentTemplate() {
    return `"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { BarChart3, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { apiClient } from '@/lib/api-client';
{{#if includeProps}}
import { {{componentName}}Props } from './{{fileName}}.types';
{{/if}}

{{#if includeProps}}
const {{componentName}}: React.FC<{{componentName}}Props> = ({
  className,
  timeRange = '7d',
  metric = 'revenue',
  ...props
}) => {
{{else}}
interface {{componentName}}Props {
  className?: string;
  timeRange?: '1d' | '7d' | '30d' | '90d';
  metric?: string;
}

const {{componentName}}: React.FC<{{componentName}}Props> = ({
  className,
  timeRange = '7d',
  metric = 'revenue'
}) => {
{{/if}}
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // TODO: Replace with actual API call
      const response = await apiClient.get(\`/analytics/\${metric}\`, {
        time_range: timeRange
      });
      
      setAnalyticsData(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange, metric]);

  // Calculate trend
  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (trend < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-gray-500" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-green-500';
    if (trend < 0) return 'text-red-500';
    return 'text-gray-500';
  };

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {{description}}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchAnalyticsData}
            className="mt-2"
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          {{description}}
        </CardTitle>
        <CardDescription>
          Analytics for {timeRange} period
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Main Metric */}
            <div className="text-center">
              <div className="text-3xl font-bold">
                {analyticsData?.currentValue?.toLocaleString() || '---'}
              </div>
              <div className="flex items-center justify-center gap-2 mt-2">
                {analyticsData?.trend && getTrendIcon(analyticsData.trend)}
                <span className={cn(
                  "text-sm font-medium",
                  getTrendColor(analyticsData?.trend || 0)
                )}>
                  {Math.abs(analyticsData?.trend || 0).toFixed(1)}%
                </span>
                <Badge variant="outline">
                  vs previous period
                </Badge>
              </div>
            </div>

            {/* Additional Metrics */}
            {analyticsData?.metrics && (
              <div className="grid grid-cols-2 gap-4">
                {analyticsData.metrics.map((metricItem: any, index: number) => (
                  <div key={index} className="text-center p-3 bg-muted rounded-lg">
                    <div className="text-lg font-semibold">
                      {metricItem.value?.toLocaleString() || '---'}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {metricItem.label}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Time Range Selector */}
            <div className="flex gap-2 justify-center mt-4">
              {['1d', '7d', '30d', '90d'].map((range) => (
                <Button
                  key={range}
                  variant={timeRange === range ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    // TODO: Update time range
                    console.log('Update time range to:', range);
                  }}
                >
                  {range}
                </Button>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default {{componentName}};`;
  }

  /**
   * Process template with configuration
   */
  processTemplate(template, config) {
    let processed = template;
    
    // Replace placeholders
    processed = processed.replace(/\{\{componentName\}\}/g, config.componentName);
    processed = processed.replace(/\{\{fileName\}\}/g, config.fileName);
    processed = processed.replace(/\{\{description\}\}/g, config.description || 'Generated component');

    // Process conditional blocks
    processed = this.processConditionals(processed, config);

    return processed;
  }

  /**
   * Process conditional template blocks
   */
  processConditionals(template, config) {
    // Handle {{#if includeProps}} blocks
    const ifPropsRegex = /\{\{#if includeProps\}\}([\s\S]*?)\{\{\/if\}\}/g;
    template = template.replace(ifPropsRegex, config.includeProps ? '$1' : '');

    // Handle {{#if includeHooks}} blocks
    const ifHooksRegex = /\{\{#if includeHooks\}\}([\s\S]*?)\{\{\/if\}\}/g;
    template = template.replace(ifHooksRegex, config.includeHooks ? '$1' : '');

    // Handle {{else}} blocks
    const ifElseRegex = /\{\{#if (\w+)\}\}([\s\S]*?)\{\{else\}\}([\s\S]*?)\{\{\/if\}\}/g;
    template = template.replace(ifElseRegex, (match, condition, ifBlock, elseBlock) => {
      return config[condition] ? ifBlock : elseBlock;
    });

    return template;
  }

  /**
   * Generate props interface template
   */
  getPropsTemplate(config) {
    return `// ${config.componentName} Props Interface
// Generated by BiteBase Intelligence Component Generator

export interface ${config.componentName}Props {
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Component title
   */
  title?: string;
  
  /**
   * ${config.description}
   */
  // TODO: Add specific props for your component
  
  /**
   * Loading state
   */
  loading?: boolean;
  
  /**
   * Error state
   */
  error?: string | null;
  
  /**
   * Data for the component
   */
  data?: any;
  
  /**
   * Callback functions
   */
  onRefresh?: () => void;
  onChange?: (value: any) => void;
  onClick?: () => void;
}

export interface ${config.componentName}Data {
  // TODO: Define your data structure
  id: string;
  name: string;
  value: number;
  timestamp: Date;
}

export interface ${config.componentName}Config {
  // TODO: Define your configuration structure
  refreshInterval?: number;
  autoRefresh?: boolean;
  theme?: 'light' | 'dark';
}
`;
  }

  /**
   * Generate custom hook template
   */
  getHookTemplate(config) {
    return `// use${config.componentName} Hook
// Generated by BiteBase Intelligence Component Generator

import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/lib/api-client';
${config.includeProps ? `import { ${config.componentName}Data, ${config.componentName}Config } from './${config.fileName}.types';` : ''}

interface Use${config.componentName}Result {
  data: any;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  reset: () => void;
}

export const use${config.componentName} = (
  config?: ${config.includeProps ? `${config.componentName}Config` : 'any'}
): Use${config.componentName}Result => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch data function
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // TODO: Replace with actual API call
      const response = await apiClient.get('/api/v1/data');
      setData(response.data);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  }, []);

  // Reset function
  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  // Auto-fetch on mount
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Auto-refresh if configured
  useEffect(() => {
    if (config?.autoRefresh && config.refreshInterval) {
      const interval = setInterval(fetchData, config.refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchData, config?.autoRefresh, config?.refreshInterval]);

  return {
    data,
    loading,
    error,
    refresh: fetchData,
    reset
  };
};
`;
  }

  /**
   * Generate test template
   */
  getTestTemplate(config) {
    return `// ${config.componentName} Tests
// Generated by BiteBase Intelligence Component Generator

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ${config.componentName} from './${config.fileName}';

// Mock API client
jest.mock('@/lib/api-client', () => ({
  apiClient: {
    get: jest.fn()
  }
}));

describe('${config.componentName}', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<${config.componentName} />);
    expect(screen.getByText('${config.description}')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    render(<${config.componentName} loading={true} />);
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('displays error state', () => {
    const errorMessage = 'Test error message';
    render(<${config.componentName} error={errorMessage} />);
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('handles refresh action', async () => {
    const mockRefresh = jest.fn();
    render(<${config.componentName} onRefresh={mockRefresh} />);
    
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);
    
    expect(mockRefresh).toHaveBeenCalledTimes(1);
  });

  it('applies custom className', () => {
    const customClass = 'custom-test-class';
    render(<${config.componentName} className={customClass} />);
    
    const component = screen.getByTestId('${config.fileName.toLowerCase()}');
    expect(component).toHaveClass(customClass);
  });

  it('displays data correctly', () => {
    const testData = {
      value: 1234,
      label: 'Test Label'
    };
    
    render(<${config.componentName} data={testData} />);
    expect(screen.getByText('1,234')).toBeInTheDocument();
    expect(screen.getByText('Test Label')).toBeInTheDocument();
  });

  ${config.includeHooks ? `
  it('uses custom hook correctly', async () => {
    const { apiClient } = require('@/lib/api-client');
    apiClient.get.mockResolvedValue({ data: { value: 100 } });

    render(<${config.componentName} />);
    
    await waitFor(() => {
      expect(screen.getByText('100')).toBeInTheDocument();
    });
  });
  ` : ''}
});

// Accessibility tests
describe('${config.componentName} Accessibility', () => {
  it('has proper ARIA labels', () => {
    render(<${config.componentName} />);
    
    const component = screen.getByRole('region');
    expect(component).toHaveAttribute('aria-label');
  });

  it('supports keyboard navigation', () => {
    render(<${config.componentName} />);
    
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    refreshButton.focus();
    expect(refreshButton).toHaveFocus();
  });

  it('has sufficient color contrast', () => {
    // This would typically use a tool like axe-core
    render(<${config.componentName} />);
    // Add specific contrast checks based on your design system
  });
});
`;
  }

  /**
   * Generate documentation template
   */
  getDocumentationTemplate(config) {
    return `# ${config.componentName}

${config.description}

## Overview

This component was generated using the BiteBase Intelligence Component Generator and provides ${config.description.toLowerCase()}.

## Usage

\`\`\`tsx
import { ${config.componentName} } from '@/components/${config.componentType}';

function MyPage() {
  return (
    <${config.componentName}
      title="My ${config.componentName}"
      className="my-custom-class"
    />
  );
}
\`\`\`

## Props

${config.includeProps ? `
See \`${config.fileName}.types.ts\` for complete prop definitions.

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| className | string | undefined | Additional CSS classes |
| title | string | "${config.description}" | Component title |
| loading | boolean | false | Loading state |
| error | string | null | Error message |
` : `
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| className | string | undefined | Additional CSS classes |
| title | string | "${config.description}" | Component title |
`}

## Features

- ✅ Responsive design
- ✅ Loading states
- ✅ Error handling
- ✅ Accessibility support
- ✅ TypeScript support
${config.includeHooks ? '- ✅ Custom hooks for data management' : ''}
${config.includeTests ? '- ✅ Comprehensive test coverage' : ''}

## Styling

This component uses ${config.styleType === 'tailwind' ? 'Tailwind CSS' : config.styleType} for styling and follows the BiteBase Intelligence design system.

## API Integration

${config.componentType.includes('dashboard') || config.componentType.includes('analytics') ? `
This component integrates with the BiteBase Intelligence API:

\`\`\`typescript
// Example API call
const data = await apiClient.get('/api/v1/analytics');
\`\`\`
` : 'Update this section with specific API endpoints used by this component.'}

## Testing

${config.includeTests ? `
Run tests with:

\`\`\`bash
npm test ${config.fileName}.test.tsx
\`\`\`

The component includes:
- Unit tests for all props and behaviors
- Accessibility tests
- Integration tests with API mocking
` : 'Add tests using the BiteBase Intelligence testing patterns.'}

## Performance Considerations

- Component is memoized to prevent unnecessary re-renders
- Data fetching is optimized with proper loading states
- Implements proper error boundaries

## Customization

### Theming

The component respects the global theme configuration and can be customized through:

1. CSS custom properties
2. Tailwind CSS utilities
3. Component props

### Data Processing

Override the default data processing by providing custom transformers:

\`\`\`tsx
<${config.componentName}
  dataTransformer={(data) => processMyData(data)}
/>
\`\`\`

## Examples

### Basic Usage

\`\`\`tsx
<${config.componentName} />
\`\`\`

### With Custom Configuration

\`\`\`tsx
<${config.componentName}
  title="Custom Title"
  refreshInterval={60000}
  className="border-2 border-blue-500"
/>
\`\`\`

### Error Handling

\`\`\`tsx
<${config.componentName}
  onError={(error) => console.error('Component error:', error)}
  fallback={<div>Something went wrong</div>}
/>
\`\`\`

## Contributing

When modifying this component:

1. Update prop types in \`${config.fileName}.types.ts\`
2. Add corresponding tests
3. Update this documentation
4. Follow the BiteBase Intelligence coding standards

## Related Components

- [Dashboard Components](../dashboard/README.md)
- [Chart Components](../charts/README.md)
- [Analytics Components](../analytics/README.md)
`;
  }

  /**
   * Generate Storybook story template
   */
  getStoryTemplate(config) {
    return `// ${config.componentName} Stories
// Generated by BiteBase Intelligence Component Generator

import type { Meta, StoryObj } from '@storybook/react';
import ${config.componentName} from './${config.fileName}';

const meta: Meta<typeof ${config.componentName}> = {
  title: 'Components/${config.componentType}/${config.componentName}',
  component: ${config.componentName},
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: '${config.description}'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes'
    },
    title: {
      control: 'text',
      description: 'Component title'
    },
    loading: {
      control: 'boolean',
      description: 'Loading state'
    },
    error: {
      control: 'text',
      description: 'Error message'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    title: '${config.description}'
  }
};

// Loading state story
export const Loading: Story = {
  args: {
    title: '${config.description}',
    loading: true
  }
};

// Error state story
export const Error: Story = {
  args: {
    title: '${config.description}',
    error: 'Failed to load data. Please try again.'
  }
};

// With data story
export const WithData: Story = {
  args: {
    title: '${config.description}',
    data: {
      value: 1234,
      trend: 15.5,
      label: 'Sample Data'
    }
  }
};

// Custom styling story
export const CustomStyling: Story = {
  args: {
    title: '${config.description}',
    className: 'border-2 border-blue-500 shadow-lg'
  }
};

// Interactive story
export const Interactive: Story = {
  args: {
    title: '${config.description}',
    onRefresh: () => console.log('Refresh clicked'),
    onChange: (value) => console.log('Value changed:', value)
  }
};
`;
  }

  // Additional component templates would go here...
  getRestaurantComponentTemplate() {
    return this.getAnalyticsComponentTemplate().replace(/Analytics/g, 'Restaurant');
  }

  getLocationComponentTemplate() {
    return this.getAnalyticsComponentTemplate().replace(/Analytics/g, 'Location');
  }

  getAIComponentTemplate() {
    return this.getAnalyticsComponentTemplate().replace(/Analytics/g, 'AI');
  }

  getReportComponentTemplate() {
    return this.getAnalyticsComponentTemplate().replace(/Analytics/g, 'Report');
  }

  getUtilityComponentTemplate() {
    return this.getDashboardWidgetTemplate();
  }

  getUIComponentTemplate() {
    return this.getDashboardWidgetTemplate();
  }
}

// CLI execution
if (require.main === module) {
  const generator = new ComponentGenerator();
  generator.generate().catch(console.error);
}

module.exports = ComponentGenerator;
`;
  }

  /**
   * Auto-install required dependencies
   */
  async installDependencies() {
    console.log(chalk.yellow('📦 Installing dependencies...'));
    
    try {
      process.chdir(this.frontendPath);
      execSync('npm install inquirer chalk', { stdio: 'inherit' });
      console.log(chalk.green('✅ Dependencies installed'));
    } catch (error) {
      console.warn(chalk.yellow('⚠️  Could not install dependencies automatically'));
      console.log('Please run: npm install inquirer chalk');
    }
  }
}

// CLI execution
if (require.main === module) {
  const generator = new ComponentGenerator();
  generator.generate().catch(console.error);
}

module.exports = ComponentGenerator;